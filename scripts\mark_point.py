#!/usr/bin/env python3
"""
标记点记录脚本
在建图过程中记录特定位置的3D坐标点
使用方法：
1. 启动建图：roslaunch faster_lio mapping_mid360.launch rviz:=true
2. 运行此脚本：python3 scripts/mark_point.py
3. 按空格键在当前雷达位置标记点
4. 按'q'退出并保存所有标记点
"""

import rospy
import numpy as np
import json
import os
import time
from datetime import datetime
from nav_msgs.msg import Odometry
from geometry_msgs.msg import PointStamped
from visualization_msgs.msg import Marker, MarkerArray
import tf2_ros
import tf2_geometry_msgs
import threading
import sys
import select
import tty
import termios

class PointMarker:
    def __init__(self):
        rospy.init_node('point_marker', anonymous=True)
        
        # 存储标记点
        self.marked_points = []
        self.current_pose = None
        
        # ROS订阅和发布
        self.odom_sub = rospy.Subscriber('/Odometry', Odometry, self.odom_callback)
        self.marker_pub = rospy.Publisher('/marked_points', MarkerArray, queue_size=10)
        
        # TF监听器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 文件保存路径
        self.save_dir = os.path.expanduser("~/faster_lio/marked_points")
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
        
        # 键盘输入设置
        self.old_settings = termios.tcgetattr(sys.stdin)
        tty.setraw(sys.stdin.fileno())
        
        print("\n" + "="*10)
        print("e标记点记录器")
        print("="*10)
        print("操作说明:\n")
        print("  [空格键] - 在当前雷达位置标记点\n")
        print("  [q键]    - 退出并保存所有标记点\t")
        print("  [l键]    - 列出所有已标记的点\n")
        print("  [c键]    - 清除所有标记点\t")
        print("="*10)
        print("⏳ 等待里程计数据...")
        print("   (请确保faster_lio建图系统正在运行)\n")

    def odom_callback(self, msg):
        """接收里程计数据"""
        if self.current_pose is None:
            print("✅ 里程计数据已连接，可以开始标记点了！")
            print("   按空格键标记当前位置\n")
        self.current_pose = msg.pose.pose
        
    def get_lidar_position(self):
        """获取雷达在世界坐标系中的位置"""
        if self.current_pose is None:
            return None
            
        # 雷达位置（假设雷达在机体坐标系原点）
        position = self.current_pose.position
        return {
            'x': position.x,
            'y': position.y,
            'z': position.z,
            'timestamp': rospy.Time.now().to_sec(),
            'frame_id': 'camera_init'  # faster_lio使用的世界坐标系
        }
    
    def mark_current_point(self):
        """标记当前位置"""
        lidar_pos = self.get_lidar_position()
        if lidar_pos is None:
            print("\n⚠️  警告：无法获取当前位置")
            print("   请确保faster_lio建图系统正在运行")
            print("   检查 /Odometry 话题是否有数据\n")
            return
            
        # 添加标记点
        point_id = len(self.marked_points)
        lidar_pos['id'] = point_id
        lidar_pos['description'] = f"标记点_{point_id}"
        
        self.marked_points.append(lidar_pos)
        
        print(f"\n✓ 标记点 {point_id} 已记录")
        print(f"坐标: ({lidar_pos['x']:.3f}, {lidar_pos['y']:.3f}, {lidar_pos['z']:.3f})\n")
        print(f"时间: {time.strftime('%H:%M:%S', time.localtime(lidar_pos['timestamp']))}\n")
        
        # 发布可视化标记
        self.publish_markers()
    
    def publish_markers(self):
        """发布标记点的可视化"""
        marker_array = MarkerArray()
        
        for i, point in enumerate(self.marked_points):
            # 创建球形标记
            marker = Marker()
            marker.header.frame_id = point['frame_id']
            marker.header.stamp = rospy.Time.now()
            marker.ns = "marked_points"
            marker.id = i
            marker.type = Marker.SPHERE
            marker.action = Marker.ADD
            
            # 位置
            marker.pose.position.x = point['x']
            marker.pose.position.y = point['y']
            marker.pose.position.z = point['z']  # 雷达正上方1.63米
            marker.pose.orientation.w = 1.0
            
            # 大小和颜色
            marker.scale.x = 1.0
            marker.scale.y = 1.0
            marker.scale.z = 1.0
            marker.color.r = 1.0
            marker.color.g = 0.0
            marker.color.b = 0.0
            marker.color.a = 0.8
            
            marker_array.markers.append(marker)
            
            # 创建文本标记
            text_marker = Marker()
            text_marker.header = marker.header
            text_marker.ns = "marked_points_text"
            text_marker.id = i
            text_marker.type = Marker.TEXT_VIEW_FACING
            text_marker.action = Marker.ADD
            
            text_marker.pose.position.x = point['x']
            text_marker.pose.position.y = point['y']
            text_marker.pose.position.z = point['z'] +0.63  # 雷达正上方2.63米（1.63+1.0文字偏移）
            text_marker.pose.orientation.w = 1.0
            
            text_marker.scale.z = 1.0
            text_marker.color.r = 1.0
            text_marker.color.g = 1.0
            text_marker.color.b = 1.0
            text_marker.color.a = 1.0
            
            text_marker.text = f"Point_{i}"
            
            marker_array.markers.append(text_marker)
        
        self.marker_pub.publish(marker_array)
    
    def list_points(self):
        """列出所有标记点"""
        if not self.marked_points:
            print("\n📍 还没有标记任何点")
            return

        print(f"\n" + "="*40)
        print(f"    已标记的点 ({len(self.marked_points)}个)")
        print("="*40)
        for i, point in enumerate(self.marked_points):
            timestamp_str = time.strftime('%H:%M:%S', time.localtime(point['timestamp']))
            print(f"点 {i}: ({point['x']:.3f}, {point['y']:.3f}, {point['z']:.3f}) - {timestamp_str}")
        print("="*40)
    
    def clear_points(self):
        """清除所有标记点"""
        if not self.marked_points:
            print("\n📍 没有标记点需要清除")
            return

        count = len(self.marked_points)
        self.marked_points.clear()
        print(f"\n🗑️  已清除 {count} 个标记点")
        
        # 清除可视化
        marker_array = MarkerArray()
        marker = Marker()
        marker.action = Marker.DELETEALL
        marker_array.markers.append(marker)
        self.marker_pub.publish(marker_array)
    
    def save_points(self):
        """保存标记点到文件"""
        if not self.marked_points:
            print("\n📍 没有标记点需要保存")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存为JSON格式
        json_file = os.path.join(self.save_dir, f"marked_points_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.marked_points, f, indent=2, ensure_ascii=False)

        # 保存为简单的文本格式
        txt_file = os.path.join(self.save_dir, f"marked_points_{timestamp}.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("# 标记点坐标 (x, y, z)\n")
            f.write("# 时间戳: {}\n".format(timestamp))
            f.write("# 坐标系: camera_init (faster_lio世界坐标系)\n\n")
            for i, point in enumerate(self.marked_points):
                f.write(f"Point_{i}: {point['x']:.6f}, {point['y']:.6f}, {point['z']:.6f}\n")

        print(f"\n" + "="*50)
        print(f"💾 保存完成！")
        print(f"标记点数量: {len(self.marked_points)} 个\n")
        print(f"JSON文件: {json_file}\n")
        print(f"文本文件: {txt_file}\n")
        print("="*50)
    
    def get_key(self):
        """获取键盘输入"""
        if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
            return sys.stdin.read(1)
        return None
    
    def run(self):
        """主循环"""
        try:
            while not rospy.is_shutdown():
                key = self.get_key()
                
                if key == ' ':  # 空格键标记点
                    self.mark_current_point()
                elif key == 'q':  # 退出
                    break
                elif key == 'l':  # 列出点
                    self.list_points()
                elif key == 'c':  # 清除点
                    self.clear_points()
                
                rospy.sleep(0.1)
                
        except KeyboardInterrupt:
            pass
        finally:
            # 恢复终端设置
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)
            
            # 保存标记点
            if self.marked_points:
                self.save_points()
            
            print(f"\n" + "="*50)
            print("👋 程序退出，感谢使用！")
            print("="*50)

if __name__ == '__main__':
    try:
        marker = PointMarker()
        marker.run()
    except rospy.ROSInterruptException:
        pass
