// #include <tf/transform_broadcaster.h>
//ROS2
#include <tf2_ros/transform_broadcaster.h>
#include <tf2/utils.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <yaml-cpp/yaml.h>
#include <execution>
#include <fstream>
#include "laser_mapping.h"
#include "utils.h"
#include "slam/pose_graph_manager.h"
#include "slam/gps_factor.h"
#include "slam/utils.hpp"
#include <gtsam/slam/PriorFactor.h>
#include <gtsam/navigation/GPSFactor.h>
#include <sensor_msgs/msg/nav_sat_fix.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/float64.hpp>
#include <sstream>
#include <vector>
#include <string>
#include <iomanip>
#include <fstream>
const std::string PARAM_PATH_SAVE_EN = "path_save_en";
const std::string PARAM_PUBLISH_PATH_PUBLISH_EN ="publish.path_publish_en";
const std::string PARAM_PUBLISH_SCAN_PUBLISH_EN ="publish.scan_publish_en";
const std::string PARAM_PUBLISH_DENSE_PUBLISH_EN ="publish.dense_publish_en";
const std::string PARAM_PUBLISH_SCAN_BODYFRAME_PUB_EN ="publish.scan_bodyframe_pub_en";
const std::string PARAM_PUBLISH_SCAN_EFFECT_PUB_EN ="publish.scan_effect_pub_en";
const std::string PARAM_COMMON_TIME_SYNC_EN ="common.time_sync_en";
const std::string PARAM_FITER_SIZE_SURF ="filter_size_surf";
const std::string PARAM_FILTER_SIZE_MAP ="filter_size_map";
const std::string PARAM_CUBE_SIDE_LENGTH ="cube_side_length";
const std::string PARAM_MAPPING_DET_RNAGE ="mapping.det_range";
const std::string PARAM_MAPPING_GYR_COV ="mapping.gyr_cov";
const std::string PARAM_MAPPING_ACC_COV ="mapping.acc_cov";
const std::string PARAM_MAPPING_B_GYR_COV ="mapping.b_gyr_cov";
const std::string PARAM_MAPPING_B_ACC_COV ="mapping.b_acc_cov";
const std::string PARAM_MAX_ITERATION ="max_iteration";
const std::string PARAM_ESTI_PLANE_THRESHOLD ="esti_plane_threshold";
const std::string PARAM_PREPROCESS_BLIND ="preprocess.blind";
const std::string PARAM_PREPROCESS_TIME_SCLAE ="preprocess.time_scale"; 
const std::string PARAM_PREPROCESS_LIDAR_TYPE ="preprocess.lidar_type";
const std::string PARAM_PREPROCESS_VERTICAL_RESOLUTION ="preprocess.vertical_resolution";
const std::string PARAM_PREPROCESS_HORIZONTAL_RESOLUTION ="preprocess.horizontal_resolution";
const std::string PARAM_POINT_FILTER_NUM ="point_filter_num";
const std::string PARAM_FEATRUE_EXTRACT_ENABLE ="feature_extract_enable";  
const std::string PARAM_RUNTIME_POS_LOG_ENABLE ="runtime_pos_log_enable";
const std::string PARAM_MAPPING_EXTRINSIC_EST_EN ="mapping.extrinsic_est_en";
const std::string PARAM_PCD_SAVE_PCD_SAVE_EN ="pcd_save.pcd_save_en";
const std::string PARAM_PCA_SAVE_INTERVAL ="pcd_save.interval";
const std::string PARAM_MAPPING_EXTRINSIC_T ="mapping.extrinsic_T";
const std::string PARAM_MAPPING_EXTRINSIC_R ="mapping.extrinsic_R";
const std::string PARAM_IVOX_GRID_RESOLUTION ="ivox_grid_resolution";
const std::string PARAM_IVOX_NEARBY_TYPE ="ivox_nearby_type";

// 重力对齐相关参数常量
const std::string PARAM_GRAVITY_ALIGNMENT_ENABLE = "gravity_alignment.enable_gravity_alignment";
const std::string PARAM_GRAVITY_ALIGNMENT_ACC_DIFF_THR = "gravity_alignment.acc_diff_thr";
const std::string PARAM_GRAVITY_ALIGNMENT_NUM_MOVING_FRAMES_THR = "gravity_alignment.num_moving_frames_thr";
const std::string PARAM_GRAVITY_ALIGNMENT_NUM_GRAVITY_MEASUREMENTS_THR = "gravity_alignment.num_gravity_measurements_thr";
const std::string PARAM_GRAVITY_ALIGNMENT_G_BASE = "gravity_alignment.g_base";

// GPS因子相关参数常量
const std::string PARAM_GPS_FACTOR_ENABLE = "gps_factor.enable";
const std::string PARAM_GPS_FACTOR_BASE_SIGMA = "gps_factor.base_sigma";
const std::string PARAM_GPS_FACTOR_MIN_ACCURACY = "gps_factor.min_accuracy";
const std::string PARAM_GPS_FACTOR_MIN_CONFIDENCE = "gps_factor.min_confidence";
const std::string PARAM_GPS_FACTOR_SIGNAL_LOSS_TIMEOUT = "gps_factor.signal_loss_timeout";
const std::string PARAM_GPS_FACTOR_MAX_CONSECUTIVE_BAD = "gps_factor.max_consecutive_bad";
const std::string PARAM_GPS_FACTOR_CONSTRAINT_INTERVAL = "gps_factor.constraint_interval";
const std::string PARAM_GPS_FACTOR_TRAJECTORY_BUFFER_SIZE = "gps_factor.trajectory_buffer_size";
using namespace kiss_matcher;
namespace faster_lio {

void LaserMapping::InitROS() {
    this->declare_parameter("pose_graph_enabled", true);
    pose_graph_enabled_ = this->get_parameter("pose_graph_enabled").as_bool();
    LoadParams();
    SubAndPubToROS();
    ivox_ = std::make_shared<IVoxType>(ivox_options_);
    std::this_thread::sleep_for(std::chrono::seconds(2));
    PublishGlobalMap();
    std::vector<double> epsi(23, 0.001);
    kf_.init_dyn_share(
        get_f, df_dx, df_dw,
        [this](state_ikfom &s, esekfom::dyn_share_datastruct<double> &ekfom_data) { ObsModel(s, ekfom_data); },
        options::NUM_MAX_ITERATIONS, epsi.data());
}
void LaserMapping::PublishGlobalMap() {
    if (pub_global_map_ && known_map_cloud_ && !known_map_cloud_->empty()) {

        sensor_msgs::msg::PointCloud2 global_map_msg;
        pcl::toROSMsg(*known_map_cloud_, global_map_msg);
        global_map_msg.header.stamp = rclcpp::Clock().now();
        global_map_msg.header.frame_id = "map";
        pub_global_map_->publish(global_map_msg);

        RCLCPP_DEBUG(this->get_logger(), "发布全局地图: 原始点数=%zu, 降采样后点数=%zu",
                     known_map_cloud_->size(), known_map_cloud_->size());
    }
}
void LaserMapping::LoadParams() {
    int lidar_type, ivox_nearby_type;
    double gyr_cov, acc_cov, b_gyr_cov, b_acc_cov;
    double filter_size_surf_min;
    common::V3D lidar_T_wrt_IMU;
    common::M3D lidar_R_wrt_IMU;
    this->declare_parameter(PARAM_PATH_SAVE_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PUBLISH_PATH_PUBLISH_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PUBLISH_SCAN_PUBLISH_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PUBLISH_DENSE_PUBLISH_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PUBLISH_SCAN_BODYFRAME_PUB_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PUBLISH_SCAN_EFFECT_PUB_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_COMMON_TIME_SYNC_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_FITER_SIZE_SURF,rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_FILTER_SIZE_MAP, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_CUBE_SIDE_LENGTH,rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_MAPPING_DET_RNAGE, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_MAPPING_GYR_COV, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_MAPPING_ACC_COV,rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_MAPPING_B_GYR_COV,rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_MAPPING_B_ACC_COV ,rclcpp::PARAMETER_DOUBLE);

    this->declare_parameter(PARAM_MAX_ITERATION, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_ESTI_PLANE_THRESHOLD, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_PREPROCESS_BLIND, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_PREPROCESS_TIME_SCLAE, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_PREPROCESS_LIDAR_TYPE, rclcpp::PARAMETER_INTEGER);

    this->declare_parameter(PARAM_PREPROCESS_VERTICAL_RESOLUTION,  rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_PREPROCESS_HORIZONTAL_RESOLUTION,  rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_POINT_FILTER_NUM, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_FEATRUE_EXTRACT_ENABLE,  rclcpp::PARAMETER_BOOL);

    this->declare_parameter(PARAM_RUNTIME_POS_LOG_ENABLE, true);
    this->declare_parameter(PARAM_MAPPING_EXTRINSIC_EST_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PCD_SAVE_PCD_SAVE_EN,  rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_PCA_SAVE_INTERVAL,  rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_MAPPING_EXTRINSIC_T, rclcpp::PARAMETER_DOUBLE_ARRAY);
    this->declare_parameter(PARAM_MAPPING_EXTRINSIC_R, rclcpp::PARAMETER_DOUBLE_ARRAY);
    this->declare_parameter(PARAM_IVOX_GRID_RESOLUTION, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_IVOX_NEARBY_TYPE, rclcpp::PARAMETER_INTEGER);

    // 重力对齐参数声明
    this->declare_parameter(PARAM_GRAVITY_ALIGNMENT_ENABLE, rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_GRAVITY_ALIGNMENT_ACC_DIFF_THR, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_GRAVITY_ALIGNMENT_NUM_MOVING_FRAMES_THR, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_GRAVITY_ALIGNMENT_NUM_GRAVITY_MEASUREMENTS_THR, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_GRAVITY_ALIGNMENT_G_BASE, rclcpp::PARAMETER_DOUBLE_ARRAY);

    // GPS因子参数声明
    this->declare_parameter(PARAM_GPS_FACTOR_ENABLE, rclcpp::PARAMETER_BOOL);
    this->declare_parameter(PARAM_GPS_FACTOR_BASE_SIGMA, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_GPS_FACTOR_MIN_ACCURACY, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_GPS_FACTOR_MIN_CONFIDENCE, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_GPS_FACTOR_SIGNAL_LOSS_TIMEOUT, rclcpp::PARAMETER_DOUBLE);
    this->declare_parameter(PARAM_GPS_FACTOR_MAX_CONSECUTIVE_BAD, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_GPS_FACTOR_CONSTRAINT_INTERVAL, rclcpp::PARAMETER_INTEGER);
    this->declare_parameter(PARAM_GPS_FACTOR_TRAJECTORY_BUFFER_SIZE, rclcpp::PARAMETER_INTEGER);
    // Load basic parameters with default values
    this->get_parameter(PARAM_PATH_SAVE_EN, path_save_en_);
    this->get_parameter(PARAM_PUBLISH_PATH_PUBLISH_EN, path_pub_en_);
    this->get_parameter(PARAM_PUBLISH_SCAN_PUBLISH_EN, scan_pub_en_);
    this->get_parameter(PARAM_PUBLISH_DENSE_PUBLISH_EN, dense_pub_en_);
    this->get_parameter(PARAM_PUBLISH_SCAN_BODYFRAME_PUB_EN, scan_body_pub_en_);
    this->get_parameter(PARAM_PUBLISH_SCAN_EFFECT_PUB_EN, scan_effect_pub_en_);
    this->get_parameter("map_file_path", map_file_path_);
    this->get_parameter(PARAM_COMMON_TIME_SYNC_EN, time_sync_en_);
    this->get_parameter(PARAM_FITER_SIZE_SURF, filter_size_surf_min);
    this->get_parameter(PARAM_FILTER_SIZE_MAP, filter_size_map_min_);
    this->get_parameter(PARAM_CUBE_SIDE_LENGTH, cube_len_);
    this->get_parameter(PARAM_MAPPING_DET_RNAGE, det_range_);
    this->get_parameter(PARAM_MAPPING_GYR_COV, gyr_cov);
    this->get_parameter(PARAM_MAPPING_ACC_COV, acc_cov);
    this->get_parameter(PARAM_MAPPING_B_GYR_COV, b_gyr_cov);
    this->get_parameter(PARAM_MAPPING_B_ACC_COV, b_acc_cov);
    this->get_parameter(PARAM_MAX_ITERATION, options::NUM_MAX_ITERATIONS);
    this->get_parameter(PARAM_ESTI_PLANE_THRESHOLD, options::ESTI_PLANE_THRESHOLD);
    this->get_parameter(PARAM_PREPROCESS_BLIND, preprocess_->Blind());
    this->get_parameter(PARAM_PREPROCESS_TIME_SCLAE, preprocess_->TimeScale());
    this->get_parameter(PARAM_PREPROCESS_LIDAR_TYPE, lidar_type);
    this->get_parameter(PARAM_POINT_FILTER_NUM, preprocess_->PointFilterNum());
    this->get_parameter(PARAM_FEATRUE_EXTRACT_ENABLE, preprocess_->FeatureEnabled());
    this->get_parameter(PARAM_RUNTIME_POS_LOG_ENABLE, runtime_pos_log_);
    this->get_parameter(PARAM_MAPPING_EXTRINSIC_EST_EN, extrinsic_est_en_);
    this->get_parameter(PARAM_PCD_SAVE_PCD_SAVE_EN, pcd_save_en_);
    this->get_parameter(PARAM_PCA_SAVE_INTERVAL, pcd_save_interval_);
    this->get_parameter(PARAM_MAPPING_EXTRINSIC_T, extrinT_);
    this->get_parameter(PARAM_MAPPING_EXTRINSIC_R, extrinR_);
    this->get_parameter(PARAM_IVOX_GRID_RESOLUTION, ivox_options_.resolution_);
    this->get_parameter(PARAM_IVOX_NEARBY_TYPE, ivox_nearby_type);
    if (!this->get_parameter(PARAM_GRAVITY_ALIGNMENT_ENABLE, enable_gravity_alignment_)) {
        enable_gravity_alignment_ = false;
    }
    if (!this->get_parameter(PARAM_GRAVITY_ALIGNMENT_ACC_DIFF_THR, acc_diff_thr_)) {
        acc_diff_thr_ = 0.2;
    }
    if (!this->get_parameter(PARAM_GRAVITY_ALIGNMENT_NUM_MOVING_FRAMES_THR, num_moving_frames_thr_)) {
        num_moving_frames_thr_ = 20;
    }
    if (!this->get_parameter(PARAM_GRAVITY_ALIGNMENT_NUM_GRAVITY_MEASUREMENTS_THR, num_gravity_measurements_thr_)) {
        num_gravity_measurements_thr_ = 20;
    }
    std::vector<double> g_base_vec;
    if (!this->get_parameter(PARAM_GRAVITY_ALIGNMENT_G_BASE, g_base_vec)) {
        g_base_ = common::V3D(0.0, 0.0, -1.0);
    } else if (g_base_vec.size() == 3) {
        g_base_ = common::V3D(g_base_vec[0], g_base_vec[1], g_base_vec[2]);
    } else {
        g_base_ = common::V3D(0.0, 0.0, -1.0);
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_ENABLE, gps_factor_enable_)) {
        gps_factor_enable_ = true;
    }
    if (!this->get_parameter("gps_factor.max_gps_constraints", max_gps_constraints_)) {
        max_gps_constraints_ = 20;
    }
    if (!this->get_parameter("gps_factor.pose_cov_threshold", pose_cov_threshold_)) {
        pose_cov_threshold_ = 25.0;
    }
    current_pose_covariance_ = Eigen::MatrixXd::Identity(6, 6) * 100.0;

    if (!this->get_parameter(PARAM_GPS_FACTOR_BASE_SIGMA, gps_factor_base_sigma_)) {
        gps_factor_base_sigma_ = 1.0;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_MIN_ACCURACY, gps_factor_min_accuracy_)) {
        gps_factor_min_accuracy_ = 0.20;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_MIN_CONFIDENCE, gps_factor_min_confidence_)) {
        gps_factor_min_confidence_ = 0.3;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_SIGNAL_LOSS_TIMEOUT, gps_signal_loss_timeout_)) {
        gps_signal_loss_timeout_ = 5.0;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_MAX_CONSECUTIVE_BAD, max_consecutive_bad_gps_)) {
        max_consecutive_bad_gps_ = 10;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_CONSTRAINT_INTERVAL, gps_constraint_interval_)) {
        gps_constraint_interval_ = 10;
    }
    if (!this->get_parameter(PARAM_GPS_FACTOR_TRAJECTORY_BUFFER_SIZE, trajectory_buffer_size_)) {
        trajectory_buffer_size_ = 60;  // 默认值：保存60个轨迹点
    }
    RCLCPP_INFO(this->get_logger(), "轨迹缓冲区大小配置: %d 个轨迹点", trajectory_buffer_size_);

    LOG(INFO) << "lidar_type " << lidar_type;
    if (lidar_type == 1) {
        preprocess_->SetLidarType(LidarType::AVIA);
        LOG(INFO) << "Using AVIA Lidar";
    } else if (lidar_type == 2) {
        preprocess_->SetLidarType(LidarType::VELO32);
        LOG(INFO) << "Using Velodyne 32 Lidar";
    } else if (lidar_type == 3) {
        preprocess_->SetLidarType(LidarType::OUST64);
        LOG(INFO) << "Using OUST 64 Lidar";
    } else {
        LOG(WARNING) << "unknown lidar_type";
        // return false;

        return; 

    }

    if (ivox_nearby_type == 0) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::CENTER;
    } else if (ivox_nearby_type == 6) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY6;
    } else if (ivox_nearby_type == 18) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY18;
    } else if (ivox_nearby_type == 26) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY26;
    } else {
        LOG(WARNING) << "unknown ivox_nearby_type, use NEARBY18";
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY18;
    }

    path_.header.stamp = rclcpp::Clock().now();
    path_.header.frame_id = "map";

    voxel_scan_.setLeafSize(filter_size_surf_min, filter_size_surf_min, filter_size_surf_min);

    lidar_T_wrt_IMU = common::VecFromArray<double>(extrinT_);
    lidar_R_wrt_IMU = common::MatFromArray<double>(extrinR_);

    p_imu_->SetExtrinsic(lidar_T_wrt_IMU, lidar_R_wrt_IMU);
    p_imu_->SetGyrCov(common::V3D(gyr_cov, gyr_cov, gyr_cov));
    p_imu_->SetAccCov(common::V3D(acc_cov, acc_cov, acc_cov));
    p_imu_->SetGyrBiasCov(common::V3D(b_gyr_cov, b_gyr_cov, b_gyr_cov));
    p_imu_->SetAccBiasCov(common::V3D(b_acc_cov, b_acc_cov, b_acc_cov));
}

bool LaserMapping::LoadParamsFromYAML(const std::string &yaml_file) {
    int lidar_type, ivox_nearby_type;
    double gyr_cov, acc_cov, b_gyr_cov, b_acc_cov;
    double filter_size_surf_min;
    common::V3D lidar_T_wrt_IMU;
    common::M3D lidar_R_wrt_IMU;

    auto yaml = YAML::LoadFile(yaml_file);
    try {
        path_pub_en_ = yaml["publish"]["path_publish_en"].as<bool>();
        scan_pub_en_ = yaml["publish"]["scan_publish_en"].as<bool>();
        dense_pub_en_ = yaml["publish"]["dense_publish_en"].as<bool>();
        scan_body_pub_en_ = yaml["publish"]["scan_bodyframe_pub_en"].as<bool>();
        scan_effect_pub_en_ = yaml["publish"]["scan_effect_pub_en"].as<bool>();
        tf_imu_frame_ = yaml["publish"]["tf_imu_frame"].as<std::string>("body");
        tf_world_frame_ = yaml["publish"]["tf_world_frame"].as<std::string>("map");
        path_save_en_ = yaml["path_save_en"].as<bool>();
        options::NUM_MAX_ITERATIONS = yaml["max_iteration"].as<int>();
        options::ESTI_PLANE_THRESHOLD = yaml["esti_plane_threshold"].as<float>();
        time_sync_en_ = yaml["common"]["time_sync_en"].as<bool>();
        filter_size_surf_min = yaml["filter_size_surf"].as<float>();
        filter_size_map_min_ = yaml["filter_size_map"].as<float>();
        cube_len_ = yaml["cube_side_length"].as<int>();
        det_range_ = yaml["mapping"]["det_range"].as<float>();
        gyr_cov = yaml["mapping"]["gyr_cov"].as<float>();
        acc_cov = yaml["mapping"]["acc_cov"].as<float>();
        b_gyr_cov = yaml["mapping"]["b_gyr_cov"].as<float>();
        b_acc_cov = yaml["mapping"]["b_acc_cov"].as<float>();
        preprocess_->Blind() = yaml["preprocess"]["blind"].as<double>();
        preprocess_->TimeScale() = yaml["preprocess"]["time_scale"].as<double>();
        lidar_type = yaml["preprocess"]["lidar_type"].as<int>();
        preprocess_->NumScans() = yaml["preprocess"]["vertical_resolution"].as<int>();
        preprocess_->NumHorizontalScans() = yaml["preprocess"]["horizontal_resolution"].as<int>();
        preprocess_->PointFilterNum() = yaml["point_filter_num"].as<int>();
        preprocess_->FeatureEnabled() = yaml["feature_extract_enable"].as<bool>();
        extrinsic_est_en_ = yaml["mapping"]["extrinsic_est_en"].as<bool>();
        pcd_save_en_ = yaml["pcd_save"]["pcd_save_en"].as<bool>();
        pcd_save_interval_ = yaml["pcd_save"]["interval"].as<int>();
        extrinT_ = yaml["mapping"]["extrinsic_T"].as<std::vector<double>>();
        extrinR_ = yaml["mapping"]["extrinsic_R"].as<std::vector<double>>();

        ivox_options_.resolution_ = yaml["ivox_grid_resolution"].as<float>();
        ivox_nearby_type = yaml["ivox_nearby_type"].as<int>();
        // 读取重定位参数
        if (yaml["relocalization"]) {
            relocalization_enable_ = yaml["relocalization"]["relocalization_enable"].as<bool>(false);
            known_map_path_ = yaml["relocalization"]["known_map_path"].as<std::string>("");
            gps_relocalization_mode_ = yaml["relocalization"]["gps_relocalization_mode"].as<bool>(false);
            gps_reference_file_path_ = yaml["relocalization"]["gps_reference_file_path"].as<std::string>("");
            yaw_iteration_max_ = yaml["relocalization"]["yaw_iteration_max"].as<int>(72);
            yaw_iteration_step_ = yaml["relocalization"]["yaw_iteration_step"].as<double>(5.0);
        }
    } catch (...) {
        LOG(ERROR) << "bad conversion";
        return false;
    }
    LOG(INFO) << "lidar_type " << lidar_type;
    if (lidar_type == 1) {
        preprocess_->SetLidarType(LidarType::AVIA);
        LOG(INFO) << "Using mid360 Lidar";
    } else if (lidar_type == 2) {
        preprocess_->SetLidarType(LidarType::VELO32);
        LOG(INFO) << "Using Velodyne 32 Lidar";
    } else if (lidar_type == 3) {
        preprocess_->SetLidarType(LidarType::OUST64);
        LOG(INFO) << "Using OUST 64 Lidar";
    } else {
        LOG(WARNING) << "unknown lidar_type";
        return false;
    }
    if (ivox_nearby_type == 0) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::CENTER;
    } else if (ivox_nearby_type == 6) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY6;
    } else if (ivox_nearby_type == 18) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY18;
    } else if (ivox_nearby_type == 26) {
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY26;
    } else {
        LOG(WARNING) << "unknown ivox_nearby_type, use NEARBY18";
        ivox_options_.nearby_type_ = IVoxType::NearbyType::NEARBY18;
    }
    voxel_scan_.setLeafSize(filter_size_surf_min, filter_size_surf_min, filter_size_surf_min);
    lidar_T_wrt_IMU = common::VecFromArray<double>(extrinT_);
    lidar_R_wrt_IMU = common::MatFromArray<double>(extrinR_);
    p_imu_->SetExtrinsic(lidar_T_wrt_IMU, lidar_R_wrt_IMU);
    p_imu_->SetGyrCov(common::V3D(gyr_cov, gyr_cov, gyr_cov));
    p_imu_->SetAccCov(common::V3D(acc_cov, acc_cov, acc_cov));
    p_imu_->SetGyrBiasCov(common::V3D(b_gyr_cov, b_gyr_cov, b_gyr_cov));
    p_imu_->SetAccBiasCov(common::V3D(b_acc_cov, b_acc_cov, b_acc_cov));
    run_in_offline_ = false;
    return true;
}
void LaserMapping::SubAndPubToROS(){
    std::string lidar_topic, imu_topic;
    // ROS2
    this->declare_parameter("common.lid_topic", "livox/lidar");
    this->declare_parameter("common.imu_topic", "livox/imu");
    if (!this->get_parameter("common.lid_topic", lidar_topic)) {
        RCLCPP_WARN(this->get_logger(), "Parameter common.lid_topic not found");
    }
    if (!this->get_parameter("common.imu_topic", imu_topic)) {
        RCLCPP_WARN(this->get_logger(), "Parameter common.imu_topic not found");
    }
    // 声明参数
    this->declare_parameter("relocalization.relocalization_enable", false);
    this->declare_parameter("relocalization.known_map_path", "");
    this->declare_parameter("relocalization.gps_relocalization_mode", false);
    this->declare_parameter("relocalization.gps_reference_file_path", "");
    this->declare_parameter("relocalization.yaw_iteration_max", 72);
    this->declare_parameter("relocalization.yaw_iteration_step", 5.0);

    // 获取参数
    this->get_parameter("relocalization.relocalization_enable", relocalization_enable_);
    this->get_parameter("relocalization.known_map_path", known_map_path_);
    this->get_parameter("relocalization.gps_relocalization_mode", gps_relocalization_mode_);
    this->get_parameter("relocalization.gps_reference_file_path", gps_reference_file_path_);
    this->get_parameter("relocalization.yaw_iteration_max", yaw_iteration_max_);
    this->get_parameter("relocalization.yaw_iteration_step", yaw_iteration_step_);
    sub_livox_ = this->create_subscription<livox_ros_driver2::msg::CustomMsg>(lidar_topic, rclcpp::QoS(200000).best_effort(),
                                [this](const livox_ros_driver2::msg::CustomMsg::SharedPtr msg) { LivoxPCLCallBack(msg); });
    sub_imu_ = this->create_subscription<sensor_msgs::msg::Imu>(imu_topic, rclcpp::QoS(200000).best_effort(),
                                [this](const sensor_msgs::msg::Imu::SharedPtr msg_in) { IMUCallBack(msg_in); });
    // GPS订阅器
    sub_gps_fix_ = this->create_subscription<sensor_msgs::msg::NavSatFix>("/gps/fix", 10,
                                [this](const sensor_msgs::msg::NavSatFix::SharedPtr msg) { GPSFixCallback(msg); });
    sub_gps_gnhpr_ = this->create_subscription<std_msgs::msg::String>("/gps/gnhpr", 10,
                                [this](const std_msgs::msg::String::SharedPtr msg) { GPSGnhprCallback(msg); });
    // ROS2
    path_.header.stamp = rclcpp::Clock().now();
    path_.header.frame_id = "map";
    pub_odom_aft_mapped_ = this->create_publisher<nav_msgs::msg::Odometry>("Odometry", 100000);
    pub_path_ = this->create_publisher<nav_msgs::msg::Path>("path", 100000);
    pub_global_map_ = this->create_publisher<sensor_msgs::msg::PointCloud2>("global_map", 10);
    br  = std::make_shared<tf2_ros::TransformBroadcaster>(this);
    if (relocalization_enable_) {
        LoadKnownMap(known_map_path_);
        if (gps_relocalization_mode_) {
            loadGPSReference();
        } else {
            initial_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
                "/initialpose", 10,
                [this](const geometry_msgs::msg::PoseWithCovarianceStamped::SharedPtr msg) {
                        tf2::Quaternion q(msg->pose.pose.orientation.x, msg->pose.pose.orientation.y,
                                msg->pose.pose.orientation.z, msg->pose.pose.orientation.w);
                        tf2::Matrix3x3 qm(q);
                        double roll, pitch, yaw;
                        qm.getRPY(roll, pitch, yaw);
                        initialize_pose[0] = roll;
                        initialize_pose[1] = pitch;
                        initialize_pose[2] = yaw;
                        initialize_pose[3] = msg->pose.pose.position.x;
                        initialize_pose[4] = msg->pose.pose.position.y;
                        initialize_pose[5] = msg->pose.pose.position.z;
                        initial_pose_received_ = true;
                }
            );
            RCLCPP_INFO(this->get_logger(), "Manual relocalization mode enabled, waiting for initial pose");
        }
    }
}
LaserMapping::LaserMapping(const std::string &name) : Node(name) {
    preprocess_.reset(new PointCloudPreprocess());
    p_imu_.reset(new ImuProcess());
    InitROS();
    if (pose_graph_enabled_) {
        initializeSLAMBackend();
    }


}
Eigen::Affine3f trans2Affine3f(float transformIn[])
    {
        return pcl::getTransformation(transformIn[3], transformIn[4], transformIn[5], transformIn[0], transformIn[1], transformIn[2]);
    }
bool LaserMapping::ExecuteICPRefinement() {
    double dummy_score = 0.0;
    return ExecuteICPRefinementWithScore(dummy_score);
}
bool LaserMapping::ExecuteICPRefinementWithScore(double& fitness_score) {
    // 检查是否收到初始位姿
    if (!initial_pose_received_) {
        return false;
    }

    // Check input data
    if (!scan_undistort_ || scan_undistort_->empty() || !known_map_cloud_ || known_map_cloud_->empty()) {
        RCLCPP_ERROR(this->get_logger(), "ICP relocalization failed: invalid input data");
        return false;
    }

    // Get initial transformation matrix
    Eigen::Matrix4f initial_transform = getInitialTransformMatrix();
    if (!initial_transform.allFinite()) {
        RCLCPP_ERROR(this->get_logger(), "ICP relocalization failed: invalid initial transform");
        return false;
    }

    // Transform current scan with initial pose
    pcl::PointCloud<PointType>::Ptr transformed_scan = transformPointCloud(scan_undistort_, initial_transform);
    if (!transformed_scan || transformed_scan->empty()) {
        RCLCPP_ERROR(this->get_logger(), "ICP relocalization failed: point cloud transformation failed");
        return false;
    }

    // Perform two-stage ICP registration
    ICPResult icp_result = performTwoStageICP(transformed_scan, known_map_cloud_);
    fitness_score = icp_result.fitness_score;

    if (!icp_result.success) {
        return false;
    }

    // Calculate final transformation matrix
    Eigen::Matrix4f final_transform = icp_result.transformation * initial_transform;

    // Update system state
    updateSystemState(final_transform);

    RCLCPP_INFO(this->get_logger(), "ICP relocalization successful");
    return true;
}
Eigen::Matrix4f LaserMapping::getInitialTransformMatrix() {
    Eigen::Matrix4f transform = Eigen::Matrix4f::Identity();

    // 从initialize_pose转换为变换矩阵
    transform(0, 3) = initialize_pose[3];  // x
    transform(1, 3) = initialize_pose[4];  // y
    transform(2, 3) = initialize_pose[5];  // z

    // 从欧拉角转换为旋转矩阵
    float roll = initialize_pose[0];
    float pitch = initialize_pose[1];
    float yaw = initialize_pose[2];

    Eigen::AngleAxisf rollAngle(roll, Eigen::Vector3f::UnitX());
    Eigen::AngleAxisf pitchAngle(pitch, Eigen::Vector3f::UnitY());
    Eigen::AngleAxisf yawAngle(yaw, Eigen::Vector3f::UnitZ());

    Eigen::Matrix3f rotation = (yawAngle * pitchAngle * rollAngle).matrix();
    transform.block<3, 3>(0, 0) = rotation;

    return transform;
}
pcl::PointCloud<PointType>::Ptr LaserMapping::transformPointCloud(
    const pcl::PointCloud<PointType>::Ptr& cloud,
    const Eigen::Matrix4f& transform) {

    pcl::PointCloud<PointType>::Ptr transformed_cloud(new pcl::PointCloud<PointType>());
    transformed_cloud->reserve(cloud->size());
    transformed_cloud->resize(cloud->size());

    for (size_t i = 0; i < cloud->size(); ++i) {
        Eigen::Vector4f point(cloud->points[i].x, cloud->points[i].y, cloud->points[i].z, 1.0f);
        Eigen::Vector4f transformed_point = transform * point;

        transformed_cloud->points[i].x = transformed_point[0];
        transformed_cloud->points[i].y = transformed_point[1];
        transformed_cloud->points[i].z = transformed_point[2];
        transformed_cloud->points[i].intensity = cloud->points[i].intensity;
        transformed_cloud->points[i].normal_x = cloud->points[i].normal_x;
        transformed_cloud->points[i].normal_y = cloud->points[i].normal_y;
        transformed_cloud->points[i].normal_z = cloud->points[i].normal_z;
        transformed_cloud->points[i].curvature = cloud->points[i].curvature;
    }

    return transformed_cloud;
}
ICPResult LaserMapping::performTwoStageICP(
    const pcl::PointCloud<PointType>::Ptr& source,
    const pcl::PointCloud<PointType>::Ptr& target) {

    ICPResult result;

    // Stage 1: Coarse registration
    pcl::IterativeClosestPoint<PointType, PointType> icp_coarse;
    icp_coarse.setInputSource(source);
    icp_coarse.setInputTarget(target);
    icp_coarse.setMaxCorrespondenceDistance(2.0);
    icp_coarse.setMaximumIterations(100);
    icp_coarse.setTransformationEpsilon(1e-4);
    icp_coarse.setEuclideanFitnessEpsilon(1e-4);
    icp_coarse.setRANSACIterations(0);

    result.coarse_cloud.reset(new pcl::PointCloud<PointType>());
    icp_coarse.align(*result.coarse_cloud);

    if (!icp_coarse.hasConverged() || icp_coarse.getFitnessScore() > 0.3) {
        RCLCPP_WARN(this->get_logger(), "ICP: 粗配准失败，得分: %.6f", icp_coarse.getFitnessScore());
        result.fitness_score = icp_coarse.getFitnessScore();
        return result;
    }

    Eigen::Matrix4f coarse_transform = icp_coarse.getFinalTransformation();

    // Stage 2: Fine registration
    pcl::IterativeClosestPoint<PointType, PointType> icp_fine;
    icp_fine.setInputSource(result.coarse_cloud);
    icp_fine.setInputTarget(target);
    icp_fine.setMaxCorrespondenceDistance(0.5);
    icp_fine.setMaximumIterations(100);
    icp_fine.setTransformationEpsilon(1e-6);
    icp_fine.setEuclideanFitnessEpsilon(1e-6);
    icp_fine.setRANSACIterations(0);

    result.fine_cloud.reset(new pcl::PointCloud<PointType>());
    icp_fine.align(*result.fine_cloud);
    if (!icp_fine.hasConverged() || icp_fine.getFitnessScore() > 0.1) {
        RCLCPP_WARN(this->get_logger(), "ICP: 精配准失败，得分: %.6f", icp_fine.getFitnessScore());
        result.transformation = coarse_transform;
        result.fitness_score = icp_coarse.getFitnessScore();
        return result;
    }
    // Combine transformation matrices
    result.transformation = icp_fine.getFinalTransformation() * coarse_transform;
    result.fitness_score = icp_fine.getFitnessScore();
    result.success = true;
    RCLCPP_INFO(this->get_logger(), "ICP: 精配准成功，最终得分: %.6f", result.fitness_score);
    return result;
}

// Update system state
void LaserMapping::updateSystemState(const Eigen::Matrix4f& final_transform) {
    Eigen::Vector3f translation = final_transform.block<3, 1>(0, 3);
    Eigen::Matrix3f rotation = final_transform.block<3, 3>(0, 0);

    // Update state_point_
    state_point_.pos = translation.cast<double>();
    state_point_.rot = Eigen::Quaterniond(rotation.cast<double>());

    // Update Kalman filter state
    kf_.change_x(state_point_);
}

void LaserMapping::ResetFrontendState() {
    // Sync EKF state
    kf_.change_x(state_point_);

    // Clear local map
    if (ivox_) {
        ivox_->Clear();
    }

    // Reset key flags
    flg_first_scan_ = false;
    flg_EKF_inited_ = true;
    localmap_initialized_ = false;

    // Clear buffers while preserving recent data
    mtx_buffer_.lock();
    if (lidar_buffer_.size() > 5) {
        lidar_buffer_.erase(lidar_buffer_.begin(), lidar_buffer_.end() - 5);
    }
    if (imu_buffer_.size() > 10) {
        imu_buffer_.erase(imu_buffer_.begin(), imu_buffer_.end() - 10);
    }
    if (time_buffer_.size() > 5) {
        time_buffer_.erase(time_buffer_.begin(), time_buffer_.end() - 5);
    }
    mtx_buffer_.unlock();

    // Clear current processing data
    measures_.imu_.clear();
    measures_.lidar_.reset();
    scan_undistort_->clear();
    scan_down_body_->clear();
    scan_down_world_->clear();

    // Reset statistics
    scan_count_ = 0;
    publish_count_ = 0;
    effect_feat_num_ = 0;
    frame_num_ = 0;

    // Force gravity alignment completion after relocalization
    if (enable_gravity_alignment_) {
        is_gravity_aligned_ = true;
        RCLCPP_INFO(this->get_logger(), "Gravity alignment set to completed after relocalization");
    }

    // Reset path and relocalization state
    path_.poses.clear();
    initial_pose_received_ = false;

    RCLCPP_INFO(this->get_logger(), "Frontend state reset completed");
}
void LaserMapping::PerformRelocalization() {
    bool success = false;
    if (gps_relocalization_mode_) {
        success = performGPSAssistedRelocalization();
        if (!success) {
            return;
        }
    } else {
        success = ExecuteICPRefinement();
        if (!success) {
            initial_pose_received_ = false;
            return;
        }
    }
    if (success) {
        relocalization_state_ = RelocalizationState::RELOCALIZATION_SUCCESS;
        ResetFrontendState();
        RCLCPP_INFO(this->get_logger(), "Relocalization completed successfully");
    }
}
void LaserMapping::LoadKnownMap(const std::string& map_path) {
    if (pcl::io::loadPCDFile<PointType>(map_path, *known_map_cloud_) == -1) {
        RCLCPP_ERROR(this->get_logger(), "Failed to load known map: %s", map_path.c_str());
        return;
    }
    
    PointCloudType::Ptr filtered_map(new PointCloudType());
    filtered_map->reserve(known_map_cloud_->size());

    for (const auto& point : known_map_cloud_->points) {
        if (std::isfinite(point.x) && std::isfinite(point.y) && std::isfinite(point.z)) {
            float dist = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
            if (dist > 0.5 && dist < 200.0) {
                filtered_map->points.push_back(point);
            }
        }
    }
    float voxel_size = 0.1f;
    size_t point_count = filtered_map->size();

    if (point_count > 10000000) {
        voxel_size = 0.4f;
    } else if (point_count > 5000000) {
        voxel_size = 0.3f;
    } else if (point_count > 1000000) {
        voxel_size = 0.2f;
    }

    pcl::VoxelGrid<PointType> voxel_filter;
    voxel_filter.setInputCloud(filtered_map);
    voxel_filter.setLeafSize(voxel_size, voxel_size, voxel_size);
    voxel_filter.filter(*known_map_cloud_);
    pcl::StatisticalOutlierRemoval<PointType> sor;
    sor.setInputCloud(known_map_cloud_);
    sor.setMeanK(20);
    sor.setStddevMulThresh(2.0);
    PointCloudType::Ptr clean_map(new PointCloudType());
    sor.filter(*clean_map);
    *known_map_cloud_ = *clean_map;
}

void LaserMapping::Run() {
    if (relocalization_enable_ && relocalization_state_ != RelocalizationState::RELOCALIZATION_SUCCESS) {
        if (!SyncPackages()) {
            return;
        }
        if (gps_relocalization_mode_ && gps_data_valid_ && !initial_pose_received_) {
            initial_pose_received_ = true;
        }
        if (measures_.lidar_ && !measures_.lidar_->empty()) {
            PointCloudType::Ptr raw_scan = measures_.lidar_;
            PointCloudType::Ptr cleaned_scan(new PointCloudType());
            cleaned_scan->reserve(raw_scan->size());
            for (const auto& point : raw_scan->points) {
                if (std::isfinite(point.x) && std::isfinite(point.y) && std::isfinite(point.z)) {
                    float dist = sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
                    if (dist > 1.0 && dist < 100.0) {
                        cleaned_scan->points.push_back(point);
                    }
                }
            }
            if (!cleaned_scan->empty()) {
                pcl::VoxelGrid<PointType> voxel_filter;
                voxel_filter.setInputCloud(cleaned_scan);
                voxel_filter.setLeafSize(0.2f, 0.2f, 0.2f);
                voxel_filter.filter(*scan_undistort_);
            } else {
                RCLCPP_WARN(this->get_logger(), "重定位阶段：点云清理后为空");
                return;
            }
        } else {
            RCLCPP_WARN(this->get_logger(), "重定位阶段：当前激光帧为空");
            return;
        }
        PerformRelocalization();
        return;
    }

    if (!SyncPackages()) {
        return;
    }
    p_imu_->Process(measures_, kf_, scan_undistort_);
    if (scan_undistort_->empty() || (scan_undistort_ == nullptr)) {
        LOG(WARNING) << "No point, skip this scan!";
        return;
    }
    
    if (enable_gravity_alignment_ && !is_gravity_aligned_) {
        if (!measures_.imu_.empty()) {
            common::V3D current_acc(measures_.imu_.back()->linear_acceleration.x,
                                  measures_.imu_.back()->linear_acceleration.y,
                                  measures_.imu_.back()->linear_acceleration.z);
            if (isMotionStopped(mean_acc_stopped_, current_acc, acc_diff_thr_)) {
                global_gravity_directions_.push_back(current_acc);
                if (global_gravity_directions_.size() > num_gravity_measurements_thr_) {
                    global_gravity_directions_.pop_front();
                }
                if (global_gravity_directions_.size() >= num_gravity_measurements_thr_) {
                    common::V3D mean_gravity = common::V3D::Zero();
                    for (const auto& g : global_gravity_directions_) {
                        mean_gravity += g;
                    }
                    mean_gravity /= global_gravity_directions_.size();
                    mean_gravity.normalize();
                    R_gravity_aligned_ = computeRelativeRotation(mean_gravity, g_base_);
                    state_ikfom current_state = kf_.get_x();
                    current_state.grav = S2(-g_base_ * common::G_m_s2);
                    current_state.pos = R_gravity_aligned_ * current_state.pos;
                    current_state.rot = Eigen::Quaterniond(R_gravity_aligned_ * current_state.rot.toRotationMatrix());
                    // 更新EKF状态
                    kf_.change_x(current_state);
                    is_gravity_aligned_ = true;
                    LOG(INFO) << "Gravity alignment completed. Mean gravity: "
                             << mean_gravity.transpose() << ", Base gravity: " << g_base_.transpose();
                    LOG(INFO) << "Gravity alignment matrix will be applied to map coordinate system";
                    R_gravity_aligned_ = common::M3D::Identity();
                    return;
                }
            } else {
                RCLCPP_INFO(this->get_logger(), "正在进行重力对齐，请静止...");
                mean_acc_stopped_ = current_acc;
            }
        }
    }
    if (flg_first_scan_) {
        ivox_->AddPoints(scan_undistort_->points);
        first_lidar_time_ = measures_.lidar_bag_time_;
        flg_first_scan_ = false;
        return;
    }
    flg_EKF_inited_ = (measures_.lidar_bag_time_ - first_lidar_time_) >= options::INIT_TIME;

    /// downsample
    Timer::Evaluate(
        [&, this]() {
            voxel_scan_.setInputCloud(scan_undistort_);
            voxel_scan_.filter(*scan_down_body_);
        },
        "Downsample PointCloud");
    int cur_pts = scan_down_body_->size();
    if (cur_pts < 5) {
        LOG(WARNING) << "Too few points, skip this scan!" << scan_undistort_->size() << ", " << scan_down_body_->size();
        return;
    }
    // 添加大小检查，防止无效的向量调整
    if (cur_pts <= 0) {
        LOG(WARNING) << "Invalid cur_pts: " << cur_pts;
        return;
    }
    scan_down_world_->resize(cur_pts);
    nearest_points_.resize(cur_pts);
    residuals_.resize(cur_pts, 0);
    point_selected_surf_.resize(cur_pts, true);
    plane_coef_.resize(cur_pts, common::V4F::Zero());
    Timer::Evaluate(
        [&, this]() {
            double solve_H_time = 0;
            kf_.update_iterated_dyn_share_modified(options::LASER_POINT_COV, solve_H_time);
            state_point_ = kf_.get_x();
            euler_cur_ = SO3ToEuler(state_point_.rot);
            pos_lidar_ = state_point_.pos + state_point_.rot * state_point_.offset_T_L_I;
        },
        "IEKF Solve and Update");
    // update local map
    Timer::Evaluate([&, this]() { MapIncremental(); }, "    Incremental Mapping");
    PublishOdometry(pub_odom_aft_mapped_);
    PublishPath(pub_path_);
    processSLAMBackend();
    frame_num_++;
}
void LaserMapping::StandardPCLCallBack(const sensor_msgs::msg::PointCloud2::SharedPtr msg) {
    mtx_buffer_.lock();
    Timer::Evaluate(
        [&, this]() {
            scan_count_++;
            if ((double)msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec < last_timestamp_lidar_) {
                LOG(ERROR) << "lidar loop back, clear buffer";
                lidar_buffer_.clear();
            }
            

            PointCloudType::Ptr ptr(new PointCloudType());
            pcl::PointCloud<ouster_ros::Point> points;
            pcl::fromROSMsg(*msg, points);
            preprocess_->PCProcess(msg, ptr);
            lidar_buffer_.push_back(ptr);
            time_buffer_.push_back((double)msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec);
            last_timestamp_lidar_ =(double) msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec;
        },
        "Preprocess (Standard)");
    mtx_buffer_.unlock();
}

void LaserMapping::LivoxPCLCallBack(const livox_ros_driver2::msg::CustomMsg::SharedPtr msg) {
    mtx_buffer_.lock();
    Timer::Evaluate(
        [&, this]() {
            scan_count_++;
            if ((double)msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec < last_timestamp_lidar_) {
                LOG(ERROR) << "lidar loop back, clear buffer";
                lidar_buffer_.clear();
            }
            last_timestamp_lidar_ =(double)msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec;
            if (!time_sync_en_ && abs(last_timestamp_imu_ - last_timestamp_lidar_) > 10.0 && !imu_buffer_.empty() &&
                !lidar_buffer_.empty()) {
                LOG(INFO) << "IMU and LiDAR not Synced, IMU time: " << last_timestamp_imu_
                          << ", lidar header time: " << last_timestamp_lidar_;
            }

            if (time_sync_en_ && !timediff_set_flg_ && abs(last_timestamp_lidar_ - last_timestamp_imu_) > 1 &&
                !imu_buffer_.empty()) {
                timediff_set_flg_ = true;
                timediff_lidar_wrt_imu_ = last_timestamp_lidar_ + 0.1 - last_timestamp_imu_;
                LOG(INFO) << "Self sync IMU and LiDAR, time diff is " << timediff_lidar_wrt_imu_;
            }

            PointCloudType::Ptr ptr(new PointCloudType());
            preprocess_->PCProcess(msg, ptr);
            lidar_buffer_.emplace_back(ptr);
            time_buffer_.emplace_back(last_timestamp_lidar_);
        },
        "Preprocess (Livox)");

    mtx_buffer_.unlock();
}

void LaserMapping::IMUCallBack(const sensor_msgs::msg::Imu::SharedPtr msg_in) {
    publish_count_++;
    sensor_msgs::msg::Imu::SharedPtr msg = std::make_shared<sensor_msgs::msg::Imu>(*msg_in);
    if (abs(timediff_lidar_wrt_imu_) > 0.1 && time_sync_en_) {
        rclcpp::Time t(static_cast<uint64_t>((timediff_lidar_wrt_imu_ + ((double)msg_in->header.stamp.sec + (double)1e-9* msg_in->header.stamp.nanosec))*1e9 ));
        msg->header.stamp = t;

    }

    double timestamp =  (double)msg->header.stamp.sec + (double)1e-9* msg->header.stamp.nanosec;

    mtx_buffer_.lock();
    if (timestamp < last_timestamp_imu_) {
        LOG(WARNING) << "imu loop back, clear buffer";
        imu_buffer_.clear();
    }

    last_timestamp_imu_ = timestamp;
    imu_buffer_.emplace_back(msg);
    mtx_buffer_.unlock();
}

bool LaserMapping::SyncPackages() {
    if (lidar_buffer_.empty() || imu_buffer_.empty()) {
        return false;
    }
    if (!lidar_pushed_) {
        measures_.lidar_ = lidar_buffer_.front();
        measures_.lidar_bag_time_ = time_buffer_.front();

        if (measures_.lidar_->points.size() <= 1) {
            LOG(WARNING) << "Too few input point cloud!";
            lidar_end_time_ = measures_.lidar_bag_time_ + lidar_mean_scantime_;
        } else if (measures_.lidar_->points.back().curvature / double(1000) < 0.5 * lidar_mean_scantime_) {
            lidar_end_time_ = measures_.lidar_bag_time_ + lidar_mean_scantime_;
        } else {
            scan_num_++;
            lidar_end_time_ = measures_.lidar_bag_time_ + measures_.lidar_->points.back().curvature / double(1000);
            lidar_mean_scantime_ +=
                (measures_.lidar_->points.back().curvature / double(1000) - lidar_mean_scantime_) / scan_num_;
        }

        measures_.lidar_end_time_ = lidar_end_time_;
        lidar_pushed_ = true;
    }

    if (last_timestamp_imu_ < lidar_end_time_) {
        return false;
    }
    double imu_time = (double) imu_buffer_.front()->header.stamp.sec + (double)1e-9*imu_buffer_.front()->header.stamp.nanosec ;
    measures_.imu_.clear();
    while ((!imu_buffer_.empty()) && (imu_time < lidar_end_time_)) {
        imu_time = (double)imu_buffer_.front()->header.stamp.sec +(double) imu_buffer_.front()->header.stamp.nanosec *1e-9;
        if (imu_time > lidar_end_time_) break;
        measures_.imu_.push_back(imu_buffer_.front());
        imu_buffer_.pop_front();
    }

    lidar_buffer_.pop_front();
    time_buffer_.pop_front();
    lidar_pushed_ = false;
    return true;
}
void LaserMapping::MapIncremental() {
    PointVector points_to_add;
    PointVector point_no_need_downsample;

    int cur_pts = scan_down_body_->size();
    points_to_add.reserve(cur_pts);
    point_no_need_downsample.reserve(cur_pts);

    std::vector<size_t> index(cur_pts);
    for (size_t i = 0; i < cur_pts; ++i) {
        index[i] = i;
    }
    std::for_each(std::execution::unseq, index.begin(), index.end(), [&](const size_t &i) {
        PointBodyToWorld(&(scan_down_body_->points[i]), &(scan_down_world_->points[i]));
        PointType &point_world = scan_down_world_->points[i];
        if (!nearest_points_[i].empty() && flg_EKF_inited_) {
            const PointVector &points_near = nearest_points_[i];
            Eigen::Vector3f center =
                ((point_world.getVector3fMap() / filter_size_map_min_).array().floor() + 0.5) * filter_size_map_min_;
            Eigen::Vector3f dis_2_center = points_near[0].getVector3fMap() - center;
            if (fabs(dis_2_center.x()) > 0.5 * filter_size_map_min_ &&
                fabs(dis_2_center.y()) > 0.5 * filter_size_map_min_ &&
                fabs(dis_2_center.z()) > 0.5 * filter_size_map_min_) {
                point_no_need_downsample.emplace_back(point_world);
                return;
            }
            bool need_add = true;
            float dist = common::calc_dist(point_world.getVector3fMap(), center);
            if (points_near.size() >= options::NUM_MATCH_POINTS) {
                for (int readd_i = 0; readd_i < options::NUM_MATCH_POINTS; readd_i++) {
                    if (common::calc_dist(points_near[readd_i].getVector3fMap(), center) < dist + 1e-6) {
                        need_add = false;
                        break;
                    }
                }
            }
            if (need_add) {
                points_to_add.emplace_back(point_world);
            }
        } else {
            points_to_add.emplace_back(point_world);
        }
    });

    Timer::Evaluate(
        [&, this]() {
            ivox_->AddPoints(points_to_add);
            ivox_->AddPoints(point_no_need_downsample);
        },
        "    IVox Add Points");
}

/**
 * Lidar point cloud registration
 * will be called by the eskf custom observation model
 * compute point-to-plane residual here
 * @param s kf state
 * @param ekfom_data H matrix
 */
void LaserMapping::ObsModel(state_ikfom &s, esekfom::dyn_share_datastruct<double> &ekfom_data) {
    int cnt_pts = scan_down_body_->size();

    // 添加大小检查，防止无效的向量调整
    if (cnt_pts <= 0) {
        LOG(WARNING) << "Invalid cnt_pts: " << cnt_pts;
        ekfom_data.valid = false;
        return;
    }

    std::vector<size_t> index(cnt_pts);
    for (size_t i = 0; i < index.size(); ++i) {
        index[i] = i;
    }

    Timer::Evaluate(
        [&, this]() {
            auto R_wl = (s.rot * s.offset_R_L_I).cast<float>();
            auto t_wl = (s.rot * s.offset_T_L_I + s.pos).cast<float>();

            /** closest surface search and residual computation **/
            std::for_each(std::execution::par_unseq, index.begin(), index.end(), [&](const size_t &i) {
                PointType &point_body = scan_down_body_->points[i];
                PointType &point_world = scan_down_world_->points[i];

                /* transform to world frame */
                common::V3F p_body = point_body.getVector3fMap();
                point_world.getVector3fMap() = R_wl * p_body + t_wl;
                point_world.intensity = point_body.intensity;

                auto &points_near = nearest_points_[i];
                if (ekfom_data.converge) {
                    /** Find the closest surfaces in the map **/
                    ivox_->GetClosestPoint(point_world, points_near, options::NUM_MATCH_POINTS);
                    point_selected_surf_[i] = points_near.size() >= options::MIN_NUM_MATCH_POINTS;
                    if (point_selected_surf_[i]) {
                        point_selected_surf_[i] =
                            common::esti_plane(plane_coef_[i], points_near, options::ESTI_PLANE_THRESHOLD);
                    }
                }

                if (point_selected_surf_[i]) {
                    auto temp = point_world.getVector4fMap();
                    temp[3] = 1.0;
                    float pd2 = plane_coef_[i].dot(temp);

                    bool valid_corr = p_body.norm() > 81 * pd2 * pd2;
                    if (valid_corr) {
                        point_selected_surf_[i] = true;
                        residuals_[i] = pd2;
                    }
                }
            });
        },
        "    ObsModel (Lidar Match)");

    effect_feat_num_ = 0;

    corr_pts_.resize(cnt_pts);
    corr_norm_.resize(cnt_pts);
    for (int i = 0; i < cnt_pts; i++) {
        if (point_selected_surf_[i]) {
            corr_norm_[effect_feat_num_] = plane_coef_[i];
            corr_pts_[effect_feat_num_] = scan_down_body_->points[i].getVector4fMap();
            corr_pts_[effect_feat_num_][3] = residuals_[i];

            effect_feat_num_++;
        }
    }
    corr_pts_.resize(effect_feat_num_);
    corr_norm_.resize(effect_feat_num_);

    if (effect_feat_num_ < 1) {
        ekfom_data.valid = false;
        LOG(WARNING) << "No Effective Points!";
        return;
    }

    Timer::Evaluate(
        [&, this]() {
            /*** Computation of Measurement Jacobian matrix H and measurements vector ***/
            ekfom_data.h_x = Eigen::MatrixXd::Zero(effect_feat_num_, 12);  // 23
            ekfom_data.h.resize(effect_feat_num_);

            index.resize(effect_feat_num_);
            const common::M3F off_R = s.offset_R_L_I.toRotationMatrix().cast<float>();
            const common::V3F off_t = s.offset_T_L_I.cast<float>();
            const common::M3F Rt = s.rot.toRotationMatrix().transpose().cast<float>();

            std::for_each(std::execution::par_unseq, index.begin(), index.end(), [&](const size_t &i) {
                common::V3F point_this_be = corr_pts_[i].head<3>();
                common::M3F point_be_crossmat = SKEW_SYM_MATRIX(point_this_be);
                common::V3F point_this = off_R * point_this_be + off_t;
                common::M3F point_crossmat = SKEW_SYM_MATRIX(point_this);

                /*** get the normal vector of closest surface/corner ***/
                common::V3F norm_vec = corr_norm_[i].head<3>();

                /*** calculate the Measurement Jacobian matrix H ***/
                common::V3F C(Rt * norm_vec);
                common::V3F A(point_crossmat * C);

                if (extrinsic_est_en_) {
                    common::V3F B(point_be_crossmat * off_R.transpose() * C);
                    ekfom_data.h_x.block<1, 12>(i, 0) << norm_vec[0], norm_vec[1], norm_vec[2], A[0], A[1], A[2], B[0],
                        B[1], B[2], C[0], C[1], C[2];
                } else {
                    ekfom_data.h_x.block<1, 12>(i, 0) << norm_vec[0], norm_vec[1], norm_vec[2], A[0], A[1], A[2], 0.0,
                        0.0, 0.0, 0.0, 0.0, 0.0;
                }

                /*** Measurement: distance to the closest surface/corner ***/
                ekfom_data.h(i) = -corr_pts_[i][3];
            });
        },
        "    ObsModel (IEKF Build Jacobian)");
}

/////////////////////////////////////  debug save / show /////////////////////////////////////////////////////

// void LaserMapping::PublishPath(const ros::Publisher pub_path) {
void LaserMapping::PublishPath(const rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr pub_path) {
    SetPosestamp(msg_body_pose_);
    // msg_body_pose_.header.stamp = ros::Time().fromSec(lidar_end_time_);
    rclcpp::Time time_stamp(static_cast<uint64_t>(lidar_end_time_ * 1e9));
    msg_body_pose_.header.stamp = time_stamp;
    msg_body_pose_.header.frame_id = "map";

    /*** if path is too large, the rvis will crash ***/
    path_.poses.push_back(msg_body_pose_);
    pub_path->publish(path_);
    
}

void LaserMapping::PublishOdometry(const rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr pub_odom_aft_mapped) {
    odom_aft_mapped_.header.frame_id = "map";
    odom_aft_mapped_.child_frame_id = "laser_link";  // 改为laser_link，因为里程计基于雷达数据
    rclcpp::Time time_stamp(static_cast<uint64_t>(lidar_end_time_ * 1e9));
    odom_aft_mapped_.header.stamp = time_stamp;
    SetPosestamp(odom_aft_mapped_.pose);
    pub_odom_aft_mapped->publish(odom_aft_mapped_);
    auto P = kf_.get_P();
    for (int i = 0; i < 6; i++) {
        int k = i < 3 ? i + 3 : i - 3;
        odom_aft_mapped_.pose.covariance[i * 6 + 0] = P(k, 3);
        odom_aft_mapped_.pose.covariance[i * 6 + 1] = P(k, 4);
        odom_aft_mapped_.pose.covariance[i * 6 + 2] = P(k, 5);
        odom_aft_mapped_.pose.covariance[i * 6 + 3] = P(k, 0);
        odom_aft_mapped_.pose.covariance[i * 6 + 4] = P(k, 1);
        odom_aft_mapped_.pose.covariance[i * 6 + 5] = P(k, 2);
    }
    // ROS2
    geometry_msgs::msg::TransformStamped stampedTransform;
    stampedTransform.header.stamp = odom_aft_mapped_.header.stamp;
    stampedTransform.header.frame_id =  "map";
    stampedTransform.child_frame_id =  "laser_link";  // 改为laser_link
    stampedTransform.transform.translation.x = odom_aft_mapped_.pose.pose.position.x;
    stampedTransform.transform.translation.y = odom_aft_mapped_.pose.pose.position.y;
    stampedTransform.transform.translation.z = odom_aft_mapped_.pose.pose.position.z;
    stampedTransform.transform.rotation.w = odom_aft_mapped_.pose.pose.orientation.w;
    stampedTransform.transform.rotation.x = odom_aft_mapped_.pose.pose.orientation.x;
    stampedTransform.transform.rotation.y = odom_aft_mapped_.pose.pose.orientation.y;
    stampedTransform.transform.rotation.z = odom_aft_mapped_.pose.pose.orientation.z;
    if(!pose_graph_enabled_){
        br->sendTransform(stampedTransform);
    }
}



   
///////////////////////////  private method /////////////////////////////////////////////////////////////////////
template <typename T>
void LaserMapping::SetPosestamp(T &out) {
    out.pose.position.x = state_point_.pos(0);
    out.pose.position.y = state_point_.pos(1);
    out.pose.position.z = state_point_.pos(2);
    out.pose.orientation.x = state_point_.rot.coeffs()[0];
    out.pose.orientation.y = state_point_.rot.coeffs()[1];
    out.pose.orientation.z = state_point_.rot.coeffs()[2];
    out.pose.orientation.w = state_point_.rot.coeffs()[3];
}

void LaserMapping::PointBodyToWorld(const PointType *pi, PointType *const po) {
    common::V3D p_body(pi->x, pi->y, pi->z);
    common::V3D p_global(state_point_.rot * (state_point_.offset_R_L_I * p_body + state_point_.offset_T_L_I) +
                         state_point_.pos);

    po->x = p_global(0);
    po->y = p_global(1);
    po->z = p_global(2);
    po->intensity = pi->intensity;
}

void LaserMapping::PointBodyToWorld(const common::V3F &pi, PointType *const po) {
    common::V3D p_body(pi.x(), pi.y(), pi.z());
    common::V3D p_global(state_point_.rot * (state_point_.offset_R_L_I * p_body + state_point_.offset_T_L_I) +
                         state_point_.pos);

    po->x = p_global(0);
    po->y = p_global(1);
    po->z = p_global(2);
    po->intensity = std::abs(po->z);
}

void LaserMapping::PointBodyLidarToIMU(PointType const *const pi, PointType *const po) {
    common::V3D p_body_lidar(pi->x, pi->y, pi->z);
    common::V3D p_body_imu(state_point_.offset_R_L_I * p_body_lidar + state_point_.offset_T_L_I);

    po->x = p_body_imu(0);
    po->y = p_body_imu(1);
    po->z = p_body_imu(2);
    po->intensity = pi->intensity;
}
void LaserMapping::shutdown(){
    initial_pose_sub_.reset();
    pub_global_map_.reset();
    pub_curr_scan_.reset();
    pub_corrected_path_.reset();
    odom_back.reset();
    pub_path_.reset();
    RCLCPP_INFO(get_logger(), "Node resources cleaned up.");
}
void LaserMapping::Finish() {
    if (pcl_wait_save_->size() > 0 && !relocalization_enable_) {
        std::string file_name = std::string("scans.pcd");
        std::string all_points_dir(std::string(std::string(ROOT_DIR) + "map/") + file_name);

        // Ensure directory exists
        std::string mkdir_cmd = "mkdir -p " + std::string(ROOT_DIR) + "map/";
        int result = system(mkdir_cmd.c_str());
        if (result != 0) {
            LOG(WARNING) << "Failed to create directory: " << std::string(ROOT_DIR) + "map/";
        }

        pcl::PCDWriter pcd_writer;
        LOG(INFO) << "Saving accumulated map data: " << pcl_wait_save_->size() << " points";
        try {
            pcd_writer.writeBinary(all_points_dir, *pcl_wait_save_);
            LOG(INFO) << "Map data saved successfully";
        } catch (const std::exception& e) {
            LOG(ERROR) << "Failed to save map data: " << e.what();
        }
    }

    // Save optimized global map
    if (!relocalization_enable_) {
        std::string global_map_save_path = std::string(ROOT_DIR) + "map/global_map.pcd";
        SaveGlobalMap(global_map_save_path);

        // Save trajectory data
        if (path_.poses.size() > 0) {
            std::string trajectory_file_name = std::string("trajectory.pcd");
            std::string trajectory_dir(std::string(std::string(ROOT_DIR) + "map/") + trajectory_file_name);

            // Convert trajectory path to point cloud
            pcl::PointCloud<PointType>::Ptr trajectory_cloud(new pcl::PointCloud<PointType>());

            for (const auto& pose : path_.poses) {
                PointType point;
                point.x = pose.pose.position.x;
                point.y = pose.pose.position.y;
                point.z = pose.pose.position.z;
                point.intensity = 1.0;
                trajectory_cloud->push_back(point);
            }

            pcl::PCDWriter pcd_writer;
            LOG(INFO) << "Saving trajectory data: " << trajectory_cloud->size() << " points";
            try {
                pcd_writer.writeBinary(trajectory_dir, *trajectory_cloud);
                LOG(INFO) << "Trajectory data saved successfully";
            } catch (const std::exception& e) {
                LOG(ERROR) << "Failed to save trajectory data: " << e.what();
            }
        }
    }

    LOG(INFO) << "Finish completed";
}

common::M3D LaserMapping::computeRelativeRotation(const Eigen::Vector3d &g_a, const Eigen::Vector3d &g_b) {
    Eigen::Vector3d g_a_normalized = g_a.normalized();
    Eigen::Vector3d g_b_normalized = g_b.normalized();
    Eigen::Vector3d rotation_axis = g_a_normalized.cross(g_b_normalized);
    double rotation_angle = std::acos(std::max(-1.0, std::min(1.0, g_a_normalized.dot(g_b_normalized))));
    
    if (rotation_axis.norm() < 1e-6) {
        if (rotation_angle < M_PI / 2) {
            return Eigen::Matrix3d::Identity();
        } else {
            Eigen::Vector3d arbitrary_axis;
            if (std::abs(g_a_normalized.x()) < 0.9) {
                arbitrary_axis = Eigen::Vector3d(1, 0, 0);
            } else {
                arbitrary_axis = Eigen::Vector3d(0, 1, 0);
            }
            rotation_axis = g_a_normalized.cross(arbitrary_axis).normalized();
            rotation_angle = M_PI;
        }
    } else {
        rotation_axis.normalize();
    }
    Eigen::Matrix3d K;
    K << 0, -rotation_axis.z(), rotation_axis.y(),
         rotation_axis.z(), 0, -rotation_axis.x(),
         -rotation_axis.y(), rotation_axis.x(), 0;
    
    Eigen::Matrix3d R = Eigen::Matrix3d::Identity() + 
                        std::sin(rotation_angle) * K + 
                        (1 - std::cos(rotation_angle)) * K * K;
    
    return R;
}
bool LaserMapping::isMotionStopped(const common::V3D &acc_ref, const common::V3D &acc_curr, const double acc_diff_thr) {
    // 检查运动是否停止
    double acc_diff = (acc_curr - acc_ref).norm();
    return acc_diff < acc_diff_thr;
}

// ==================== SLAM后端功能实现 ====================

void LaserMapping::initializeSLAMBackend() {
    RCLCPP_INFO(this->get_logger(), "SLAM backend initialization started");
    gtsam::ISAM2Params parameters;
    parameters.relinearizeThreshold = 0.1;
    parameters.relinearizeSkip = 10;
    isam_ = new gtsam::ISAM2(parameters);
    LoopClosureConfig loop_closure_config;
    // 声明参数（如果不存在的话）
    this->declare_parameter("loop_closure.enable_global_registration", true);
    this->declare_parameter("loop_closure.verbose", true);
    this->declare_parameter("loop_closure.is_multilayer_env", false);
    this->declare_parameter("loop_closure.loop_detection_radius", 15.0);
    this->declare_parameter("loop_closure.loop_detection_timediff_threshold", 30.0);
    this->declare_parameter("loop_closure.num_submap_keyframes", 3);
    this->declare_parameter("loop_closure.num_inliers_threshold", 30);
    this->declare_parameter("loop_closure.voxel_resolution", 0.3);
    this->declare_parameter("loop_closure.gicp.num_threads", 4);
    this->declare_parameter("loop_closure.gicp.correspondence_randomness", 20);
    this->declare_parameter("loop_closure.gicp.max_num_iter", 20);
    this->declare_parameter("loop_closure.gicp.scale_factor_for_corr_dist", 5.0);
    this->declare_parameter("loop_closure.gicp.overlap_threshold", 15.0);
    this->declare_parameter("loop_closure.key_frame", 1.5);


    // 从参数服务器读取配置
    enable_key_frame_ = this->get_parameter("loop_closure.key_frame").as_double();
    loop_closure_config.enable_global_registration_ = this->get_parameter("loop_closure.enable_global_registration").as_bool();
    loop_closure_config.verbose_ = this->get_parameter("loop_closure.verbose").as_bool();
    loop_closure_config.is_multilayer_env_ = this->get_parameter("loop_closure.is_multilayer_env").as_bool();
    loop_closure_config.loop_detection_radius_ = this->get_parameter("loop_closure.loop_detection_radius").as_double();
    loop_closure_config.loop_detection_timediff_threshold_ = this->get_parameter("loop_closure.loop_detection_timediff_threshold").as_double();
    loop_closure_config.num_submap_keyframes_ = this->get_parameter("loop_closure.num_submap_keyframes").as_int();
    loop_closure_config.num_inliers_threshold_ = this->get_parameter("loop_closure.num_inliers_threshold").as_int();
    loop_closure_config.voxel_res_ = this->get_parameter("loop_closure.voxel_resolution").as_double();

    // 设置GICP配置
    loop_closure_config.gicp_config_.num_threads_ = this->get_parameter("loop_closure.gicp.num_threads").as_int();
    loop_closure_config.gicp_config_.correspondence_randomness_ = this->get_parameter("loop_closure.gicp.correspondence_randomness").as_int();
    loop_closure_config.gicp_config_.max_num_iter_ = this->get_parameter("loop_closure.gicp.max_num_iter").as_int();
    loop_closure_config.gicp_config_.scale_factor_for_corr_dist_ = this->get_parameter("loop_closure.gicp.scale_factor_for_corr_dist").as_double();
    loop_closure_config.gicp_config_.overlap_threshold_ = this->get_parameter("loop_closure.gicp.overlap_threshold").as_double();
    loop_closure_ = std::make_unique<LoopClosure>(loop_closure_config, this->get_logger());
    tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(this);
    // 初始化发布器
    rclcpp::QoS qos_config(1);
    qos_config.durability(RMW_QOS_POLICY_DURABILITY_TRANSIENT_LOCAL);
    qos_config.reliability(RMW_QOS_POLICY_RELIABILITY_RELIABLE);
    pub_curr_scan_ = this->create_publisher<sensor_msgs::msg::PointCloud2>("/km_sam/curr_scan", qos_config);
    pub_corrected_path_ = this->create_publisher<nav_msgs::msg::Path>("/km_sam/corrected_path", qos_config);
    // realtime_pose_pub_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("pose_stamped", qos_config);
    odom_back=this->create_publisher<nav_msgs::msg::Odometry>("odom_back", qos_config);
    // 初始化定时器
    loop_closure_timer_ = this->create_wall_timer(
        std::chrono::milliseconds(1000), // 1Hz
        [this]() {
            this->loopClosureTimerCallback();
        });
    // 初始化路径
    odom_path_.header.frame_id = "map";
    corrected_path_.header.frame_id = "map";
    RCLCPP_INFO(this->get_logger(), "SLAM后端初始化完成");
}

                                            
void LaserMapping::processSLAMBackend() {
    if (!pose_graph_enabled_) return;

    // 创建当前帧的PoseGraphNode
    auto odom_msg = std::make_shared<nav_msgs::msg::Odometry>();
    odom_msg->header.stamp = this->get_clock()->now();
    odom_msg->header.frame_id = "map";
    odom_msg->child_frame_id = "laser_link";  // 改为laser_link

    // 填充位姿信息
    odom_msg->pose.pose.position.x = state_point_.pos(0);
    odom_msg->pose.pose.position.y = state_point_.pos(1);
    odom_msg->pose.pose.position.z = state_point_.pos(2);
    odom_msg->pose.pose.orientation.x = state_point_.rot.x();
    odom_msg->pose.pose.orientation.y = state_point_.rot.y();
    odom_msg->pose.pose.orientation.z = state_point_.rot.z();
    odom_msg->pose.pose.orientation.w = state_point_.rot.w();
    
    static size_t latest_keyframe_idx_ = 0;
    // 创建点云消息
    auto cloud_msg = std::make_shared<sensor_msgs::msg::PointCloud2>();
    if (scan_undistort_ && !scan_undistort_->empty()) {
        pcl::toROSMsg(*scan_undistort_, *cloud_msg);
        cloud_msg->header.stamp = odom_msg->header.stamp;
        cloud_msg->header.frame_id = "laser_link";  // 改为laser_link
        Eigen::Matrix4d current_odom = current_frame_.pose_;
        kiss_matcher::TicToc total_timer;
        kiss_matcher::TicToc local_timer;
        current_frame_ = PoseGraphNode(
            *odom_msg, *cloud_msg, latest_keyframe_idx_, scan_voxel_res_, store_voxelized_scan_,true);
        visualizeCurrentData(current_odom, odom_msg->header.stamp, cloud_msg->header.frame_id);
        if (!is_slam_initialized_) {
            keyframes_.push_back(current_frame_);
            appendKeyframePose(current_frame_);
            auto variance_vector = (gtsam::Vector(6) << 1e-4, 1e-4, 1e-4, 1e-2, 1e-2, 1e-2).finished();
            gtsam::noiseModel::Diagonal::shared_ptr prior_noise =gtsam::noiseModel::Diagonal::Variances(variance_vector);
            gtsam_graph_.add(gtsam::PriorFactor<gtsam::Pose3>(0, eigenToGtsam(current_frame_.pose_), prior_noise));
            init_esti_.insert(0, eigenToGtsam(current_frame_.pose_));
            is_slam_initialized_ = true;
            ++latest_keyframe_idx_;  
            RCLCPP_INFO(this->get_logger(), "SLAM后端第一帧初始化完成");
        } else {
            const auto t_keyframe_processing = local_timer.toc();
            if (checkIfKeyframe(current_frame_, keyframes_.back())) {
                {
                    std::lock_guard<std::mutex> lock(keyframes_mutex_);
                    keyframes_.push_back(current_frame_);
                }
                auto variance_vector = (gtsam::Vector(6) << 1e-4, 1e-4, 1e-4, 1e-2, 1e-2, 1e-2).finished();
                gtsam::noiseModel::Diagonal::shared_ptr odom_noise =
                    gtsam::noiseModel::Diagonal::Variances(variance_vector);

                gtsam::Pose3 pose_from = eigenToGtsam(keyframes_[latest_keyframe_idx_-1].pose_corrected_);
                gtsam::Pose3 pose_to = eigenToGtsam(current_frame_.pose_corrected_);
                {
                    std::lock_guard<std::mutex> lock(graph_mutex_);
                    gtsam_graph_.add(gtsam::BetweenFactor<gtsam::Pose3>(
                        latest_keyframe_idx_-1, latest_keyframe_idx_, pose_from.between(pose_to), odom_noise));
                    init_esti_.insert(latest_keyframe_idx_, pose_to);
                }
                updateGPSConstraintStatus();
                if(auto_alignment_completed_){
                    if (shouldAddGPSConstraintPeriodically(latest_keyframe_idx_)) {
                        addGPSConstraintToGraph(latest_keyframe_idx_);
                    }
                }
                ++latest_keyframe_idx_;
                {
                    std::lock_guard<std::mutex> lock(vis_mutex_);
                    appendKeyframePose(current_frame_);
                }
                detectLoopClosureByNNSearch();
                local_timer.tic();
                try {
                    std::lock_guard<std::mutex> lock(graph_mutex_);
                    isam_->update(gtsam_graph_, init_esti_);
                    isam_->update();
                    if (loop_closure_added_) {
                        isam_->update();
                        isam_->update();
                        isam_->update();
                        isam_->update();
                        isam_->update();
                    }
                    gtsam_graph_.resize(0);
                    init_esti_.clear();
                } catch (const gtsam::IndeterminantLinearSystemException& e) {
                    RCLCPP_ERROR(this->get_logger(), "GTSAM优化失败 - 线性系统病态: %s", e.what());
                    RCLCPP_ERROR(this->get_logger(), "可能原因: GPS约束过强或数据异常，暂时禁用GPS约束");
                    gps_constraint_enabled_ = false;
                    consecutive_bad_gps_count_ = max_consecutive_bad_gps_;
                    gtsam_graph_.resize(0);
                    init_esti_.clear();
                    return;
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(this->get_logger(), "GTSAM优化失败: %s", e.what());
                    gtsam_graph_.resize(0);
                    init_esti_.clear();
                    return;
                }
                const auto t_optim = local_timer.toc();
                // 更新实时位姿
                {
                    std::lock_guard<std::mutex> lock(realtime_pose_mutex_);
                    corrected_esti_ = isam_->calculateEstimate();
                    last_corrected_pose_ =  gtsamToEigen(corrected_esti_.at<gtsam::Pose3>(corrected_esti_.size() - 1));
                    odom_delta_ = Eigen::Matrix4d::Identity();
                }
                try {
                    if (corrected_esti_.size() > 0) {
                        current_pose_covariance_ = isam_->marginalCovariance(corrected_esti_.size() - 1);
                        double x_variance = current_pose_covariance_(3, 3);
                        double y_variance = current_pose_covariance_(4, 4);
                    }
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "无法计算位姿协方差: %s", e.what());
                    current_pose_covariance_ = Eigen::MatrixXd::Identity(6, 6) * 100.0;
                }
                if (loop_closure_added_) {
                    std::lock_guard<std::mutex> lock(keyframes_mutex_);
                    for (size_t i = 0; i < corrected_esti_.size() && i < keyframes_.size(); ++i) {
                        keyframes_[i].pose_corrected_ = gtsamToEigen(
                            corrected_esti_.at<gtsam::Pose3>(i));
                    }
                    loop_closure_added_ = false;
                }
                const auto t_total = total_timer.toc();
        }
        publishSLAMResults();
        frame_count_++;
    }
}
}

void LaserMapping::visualizeCurrentData(const Eigen::Matrix4d &current_odom,
                                            const rclcpp::Time &timestamp,
                                            const std::string &frame_id) {
  {
    std::lock_guard<std::mutex> lock(realtime_pose_mutex_);
    odom_delta_                    = odom_delta_ * current_odom.inverse() * current_frame_.pose_;
    current_frame_.pose_corrected_ = last_corrected_pose_ * odom_delta_;

    geometry_msgs::msg::PoseStamped ps =
        eigenToPoseStamped(current_frame_.pose_corrected_, "map");
    // realtime_pose_pub_->publish(ps);
    nav_msgs::msg::Odometry odom;
    odom.header = ps.header;
    odom.header.frame_id = ps.header.frame_id; // 继承 "map" 框架
    odom.child_frame_id = "laser_link"; // 设置为laser_link，因为里程计基于雷达数据
    // 复制位姿
    odom.pose.pose = ps.pose;

    // 设置位姿协方差（示例：初始化为零，实际使用中需根据传感器数据填充）
    odom.pose.covariance = {0.0};

    // 设置速度（无速度信息时初始化为零）
    odom.twist.twist.linear.x = 0.0;
    odom.twist.twist.linear.y = 0.0;
    odom.twist.twist.linear.z = 0.0;
    odom.twist.twist.angular.x = 0.0;
    odom.twist.twist.angular.y = 0.0;
    odom.twist.twist.angular.z = 0.0;
    odom.twist.covariance = {0.0};
    odom_back->publish(odom);
    // 发布TF变换
    geometry_msgs::msg::TransformStamped transform_stamped;
    transform_stamped.header.stamp    = timestamp;
    transform_stamped.header.frame_id = "map";
    transform_stamped.child_frame_id  = "laser_link";  // 改为laser_link
    Eigen::Quaterniond q(current_frame_.pose_corrected_.block<3, 3>(0, 0));
    transform_stamped.transform.translation.x = current_frame_.pose_corrected_(0, 3);
    transform_stamped.transform.translation.y = current_frame_.pose_corrected_(1, 3);
    transform_stamped.transform.translation.z = current_frame_.pose_corrected_(2, 3);
    transform_stamped.transform.rotation.x    = q.x();
    transform_stamped.transform.rotation.y    = q.y();
    transform_stamped.transform.rotation.z    = q.z();
    transform_stamped.transform.rotation.w    = q.w();
    tf_broadcaster_->sendTransform(transform_stamped);
  }
}

bool LaserMapping::checkIfKeyframe(const PoseGraphNode& current_frame, const PoseGraphNode& latest_keyframe) {
    return enable_key_frame_ < (latest_keyframe.pose_corrected_.block<3, 1>(0, 3) -
                            current_frame.pose_corrected_.block<3, 1>(0, 3)).norm();
}

void LaserMapping::appendKeyframePose(const PoseGraphNode& keyframe) {
    odoms_.points.emplace_back(keyframe.pose_(0, 3), keyframe.pose_(1, 3), keyframe.pose_(2, 3));
    corrected_odoms_.points.emplace_back(
        keyframe.pose_corrected_(0, 3), keyframe.pose_corrected_(1, 3), keyframe.pose_corrected_(2, 3));
    odom_path_.poses.emplace_back(eigenToPoseStamped(keyframe.pose_, "map"));
    corrected_path_.poses.emplace_back(eigenToPoseStamped(keyframe.pose_corrected_, "map"));
    return;
}

void LaserMapping::publishSLAMResults() {
    if (!pose_graph_enabled_) return;

    // 发布当前扫描
    if (scan_undistort_ && !scan_undistort_->empty()) {
        PointCloudType::Ptr downsampled_map(new PointCloudType());
        pcl::VoxelGrid<PointType> voxel_filter;
        voxel_filter.setInputCloud(scan_undistort_);
        voxel_filter.setLeafSize(0.4f, 0.4f, 0.4f);  // 20cm体素大小，可根据需要调整
        voxel_filter.filter(*downsampled_map);

        sensor_msgs::msg::PointCloud2 curr_scan_msg;
        pcl::toROSMsg(*downsampled_map, curr_scan_msg);
        curr_scan_msg.header.stamp = this->get_clock()->now();
        curr_scan_msg.header.frame_id = "laser_link";  // 改为laser_link
        pub_curr_scan_->publish(curr_scan_msg);
    }
    // 发布校正后的路径
    if (!keyframes_.empty() && !corrected_esti_.empty()) {
        nav_msgs::msg::Path path_msg;
        path_msg.header.stamp = this->get_clock()->now();
        path_msg.header.frame_id = "map";

        for (size_t i = 0; i < keyframes_.size(); ++i) {
            geometry_msgs::msg::PoseStamped pose_stamped;
            pose_stamped.header.stamp = rclcpp::Time(keyframes_[i].timestamp_ * 1e9);
            pose_stamped.header.frame_id = "map";

            if (corrected_esti_.exists(i)) {
                Eigen::Matrix4d corrected_pose = gtsamToEigen(
                    corrected_esti_.at<gtsam::Pose3>(i));
                pose_stamped.pose.position.x = corrected_pose(0, 3);
                pose_stamped.pose.position.y = corrected_pose(1, 3);
                pose_stamped.pose.position.z = corrected_pose(2, 3);

                Eigen::Quaterniond q(corrected_pose.block<3,3>(0,0));
                pose_stamped.pose.orientation.x = q.x();
                pose_stamped.pose.orientation.y = q.y();
                pose_stamped.pose.orientation.z = q.z();
                pose_stamped.pose.orientation.w = q.w();
            } else {
                // 使用原始位姿
                pose_stamped.pose.position.x = keyframes_[i].pose_(0, 3);
                pose_stamped.pose.position.y = keyframes_[i].pose_(1, 3);
                pose_stamped.pose.position.z = keyframes_[i].pose_(2, 3);

                Eigen::Quaterniond q(keyframes_[i].pose_.block<3,3>(0,0));
                pose_stamped.pose.orientation.x = q.x();
                pose_stamped.pose.orientation.y = q.y();
                pose_stamped.pose.orientation.z = q.z();
                pose_stamped.pose.orientation.w = q.w();
            }

            path_msg.poses.push_back(pose_stamped);
        }
        pub_corrected_path_->publish(path_msg);
    }
}

// GTSAM转换函数
gtsam::Pose3 LaserMapping::eigenToGtsam(const Eigen::Matrix4d& pose) {
    return gtsam::Pose3(gtsam::Rot3(pose.block<3,3>(0,0)), gtsam::Point3(pose.block<3,1>(0,3)));
}

Eigen::Matrix4d LaserMapping::gtsamToEigen(const gtsam::Pose3& pose) {
    Eigen::Matrix4d result = Eigen::Matrix4d::Identity();
    result.block<3,3>(0,0) = pose.rotation().matrix();
    result.block<3,1>(0,3) = pose.translation();
    return result;
}


void LaserMapping::loopClosureTimerCallback() {
    performRegistration();
}

void LaserMapping::detectLoopClosureByNNSearch() {
    auto &query = keyframes_.back();
    if (!is_slam_initialized_ || keyframes_.empty()) {
        return;
    }
    query.nnsearch_processed_ = true;

    const auto &loop_idx_pairs = loop_closure_->fetchLoopCandidates(query, keyframes_);

    for (const auto &loop_candidate : loop_idx_pairs) {
        loop_idx_pair_queue_.push(loop_candidate);
    }
}

void LaserMapping::performRegistration() {
    kiss_matcher::TicToc reg_timer;
    if (loop_idx_pair_queue_.empty()) {
        return;
    }
    const auto [query_idx, match_idx] = loop_idx_pair_queue_.front();
    loop_idx_pair_queue_.pop();

    const RegOutput &reg_output = loop_closure_->performLoopClosure(keyframes_, query_idx, match_idx);
    need_lc_cloud_vis_update_   = true;

    if (reg_output.is_valid_) {
        RCLCPP_INFO(this->get_logger(), "回环闭合成功. 重叠度: %.3f", reg_output.overlapness_);
        gtsam::Pose3 pose_from = eigenToGtsam(reg_output.pose_ * keyframes_[query_idx].pose_corrected_);
        gtsam::Pose3 pose_to = eigenToGtsam(keyframes_[match_idx].pose_corrected_);

        auto variance_vector = (gtsam::Vector(6) << 1e-4, 1e-4, 1e-4, 1e-2, 1e-2, 1e-2).finished();
        gtsam::noiseModel::Diagonal::shared_ptr loop_noise =
            gtsam::noiseModel::Diagonal::Variances(variance_vector);

        {
            std::lock_guard<std::mutex> graph_lock(graph_mutex_);
            gtsam_graph_.add(gtsam::BetweenFactor<gtsam::Pose3>(
                query_idx, match_idx, pose_from.between(pose_to), loop_noise));
        }
        vis_loop_edges_.emplace_back(query_idx, match_idx);
        succeeded_query_idx_ = query_idx;
        loop_closure_added_ = true;
        need_map_update_ = true;
        need_graph_vis_update_ = true;
    } else {
        if (reg_output.overlapness_ == 0.0) {
            RCLCPP_WARN(this->get_logger(), "回环闭合失败. KISS-Matcher失败");
        } else {
            RCLCPP_WARN(this->get_logger(), "回环闭合失败. 重叠度: %.3f", reg_output.overlapness_);
        }
        need_lc_cloud_vis_update_ = false;
    }
    RCLCPP_INFO(this->get_logger(), "回环配准耗时: %.1f msec", reg_timer.toc());
    }

void LaserMapping::SaveGlobalMap(const std::string& map_file) {
    if (keyframes_.empty()) {
        RCLCPP_WARN(this->get_logger(), "没有关键帧数据，无法保存全局地图");
        return;
    }
    RCLCPP_INFO(this->get_logger(), "开始构建并保存全局地图...");
    pcl::PointCloud<PointTypefix>::Ptr global_map(new pcl::PointCloud<PointTypefix>());

    {
        std::lock_guard<std::mutex> lock(keyframes_mutex_);
        for (size_t i = 0; i < keyframes_.size(); ++i) {
            const auto &i_th_scan = [&]() {
                if (store_voxelized_scan_) {
                    return keyframes_[i].scan_;
                }
                if (keyframes_[i].voxelized_scan_.empty()) {
                    keyframes_[i].voxelized_scan_ = *kiss_matcher::voxelize(keyframes_[i].scan_, scan_voxel_res_);
                }
                return keyframes_[i].voxelized_scan_;
            }();
            *global_map += kiss_matcher::transformPcd(i_th_scan, keyframes_[i].pose_corrected_);
        }
    }
    std::filesystem::path map_path(map_file);
    std::filesystem::create_directories(map_path.parent_path());

    // 保存地图到文件
    try {
        pcl::io::savePCDFileBinary(map_file, *global_map);
        RCLCPP_INFO(this->get_logger(), "全局地图已保存到: %s (包含 %zu 个点)", 
                   map_file.c_str(), global_map->size());
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "保存全局地图失败: %s", e.what());
    }
}


// GPS回调函数实现
void LaserMapping::GPSFixCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg) {
    // 更新GPS质量状态
    current_gps_quality_ = msg->status.status;

    // 严格的GPS信号质量检测：只接受RTK固定解
    if (msg->status.status != 4) {  // 只接受RTK固定解(status=4)
        gps_data_valid_ = false;
        if (msg->status.status == 5) {
            // RCLCPP_INFO(this->get_logger(), "GPS信号质量不足：RTK浮点解，需要固定解");
        } else if (msg->status.status < 4) {
            // RCLCPP_INFO(this->get_logger(), "GPS信号质量不足：状态=%d，需要RTK固定解", msg->status.status);
        }
        return;
    }
    if (msg->position_covariance_type == sensor_msgs::msg::NavSatFix::COVARIANCE_TYPE_KNOWN ||
        msg->position_covariance_type == sensor_msgs::msg::NavSatFix::COVARIANCE_TYPE_DIAGONAL_KNOWN) {

        double horizontal_std = std::sqrt(std::max(msg->position_covariance[0], msg->position_covariance[4]));
        double vertical_std = std::sqrt(msg->position_covariance[8]);
 
        if (horizontal_std > 0.05 || vertical_std > 0.10) {
            gps_data_valid_ = false;
            return;
        }
        // 存储真实的精度信息供后续使用
        current_gps_horizontal_accuracy_ = horizontal_std;
        current_gps_vertical_accuracy_ = vertical_std;
    } else if (msg->position_covariance_type == sensor_msgs::msg::NavSatFix::COVARIANCE_TYPE_APPROXIMATED) {
        double horizontal_std = std::sqrt(std::max(msg->position_covariance[0]/20, msg->position_covariance[4]/20));
        double vertical_std = std::sqrt(msg->position_covariance[8]/20);
        if (horizontal_std > 0.35 && vertical_std > 0.50) {
            gps_data_valid_ = false;
            return;
        }
        // 存储真实的精度信息供后续使用
        current_gps_horizontal_accuracy_ = horizontal_std;
        current_gps_vertical_accuracy_ = vertical_std;
    } else if (msg->position_covariance_type == sensor_msgs::msg::NavSatFix::COVARIANCE_TYPE_UNKNOWN) {
        bool has_valid_covariance = false;
        for (int i = 0; i < 9; i++) {
            if (msg->position_covariance[i] > 0.0 && std::isfinite(msg->position_covariance[i])) {
                has_valid_covariance = true;
                break;
            }
        }
        if (has_valid_covariance) {
            double horizontal_std = std::sqrt(std::max(msg->position_covariance[0], msg->position_covariance[4]));
            double vertical_std = std::sqrt(msg->position_covariance[8]);
            if (horizontal_std > 0.20 || vertical_std > 0.50) {
                gps_data_valid_ = false;
                return;
            }
            current_gps_horizontal_accuracy_ = horizontal_std;
            current_gps_vertical_accuracy_ = vertical_std;
        } else {
            current_gps_horizontal_accuracy_ = 0.25;  // 默认15cm水平精度
            current_gps_vertical_accuracy_ = 0.50;    // 默认30cm垂直精度
        }
    } else {
        gps_data_valid_ = false;
        RCLCPP_INFO(this->get_logger(), "GPS协方差类型不支持: %d", msg->position_covariance_type);
        return;
    }
    double utm_x, utm_y;
    int zone;
    char hemisphere;
    latLonToUTM(msg->latitude, msg->longitude, utm_x, utm_y, zone, hemisphere);
    if (!gps_initialized_ && !relocalization_enable_) {
        ref_utm_x_ = utm_x;
        ref_utm_y_ = utm_y;
        ref_altitude_ = msg->altitude;
        utm_zone_ = zone;
        utm_hemisphere_ = hemisphere;

        ref_slam_x_ = state_point_.pos(0);
        ref_slam_y_ = state_point_.pos(1);
        ref_slam_z_ = state_point_.pos(2);

        Eigen::Vector3d euler = SO3ToEuler(state_point_.rot);
        double slam_yaw_rad = euler[2] * M_PI / 180.0;
        ref_slam_yaw_ = slam_yaw_rad * 180.0 / M_PI;
            // 使用手动设置的旋转角度作为初始值
        double manual_rotation_deg = 0.0;  // 手动设置的旋转角度（度）
        slam_to_gps_rotation_angle_ = manual_rotation_deg * M_PI / 180.0;
        gps_initialized_ = true;
  
    }

    double gps_delta_east = utm_x - ref_utm_x_;      // GPS东向移动量
    double gps_delta_north = utm_y - ref_utm_y_;     // GPS北向移动量
    double gps_delta_z = msg->altitude - ref_altitude_; // GPS高度变化量

    // 使用旋转矩阵将GPS移动量从东北坐标系转换到SLAM坐标系
    double cos_angle = std::cos(slam_to_gps_rotation_angle_);
    double sin_angle = std::sin(slam_to_gps_rotation_angle_);
    double slam_delta_x = -sin_angle * gps_delta_east + cos_angle * gps_delta_north;
    double slam_delta_y = cos_angle * gps_delta_east + sin_angle * gps_delta_north;

    current_gps_x_ = ref_slam_x_ + slam_delta_x;
    current_gps_y_ = ref_slam_y_ - slam_delta_y;
    current_gps_z_ = ref_slam_z_ + gps_delta_z;
    gps_data_valid_ = true;

    if (!auto_alignment_completed_&& !relocalization_enable_) {
        collectTrajectoryData(current_gps_x_, current_gps_y_, current_gps_z_,
                              state_point_.pos(0), state_point_.pos(1), state_point_.pos(2));
    }
}

void LaserMapping::GPSGnhprCallback(const std_msgs::msg::String::SharedPtr msg) {
    std::string nmea = msg->data;
    std::vector<std::string> fields;
    std::stringstream ss(nmea);
    std::string field;
    while (std::getline(ss, field, ',')) {
        fields.push_back(field);
    }
    if (fields.size() >= 8) {
        try {
            double heading = std::stod(fields[2]);
            double pitch = std::stod(fields[3]);
            double roll = std::stod(fields[4]);
            int heading_status = std::stoi(fields[5]);
        } catch (const std::exception& e) {
        }
    }
}

// GPS坐标转换函数
void LaserMapping::latLonToUTM(double lat, double lon, double& utm_x, double& utm_y, int& zone, char& hemisphere) {
    // 简化的UTM转换 (适用于中国地区)
    const double a = 6378137.0; // WGS84长半轴
    const double e2 = 0.00669437999014; // WGS84第一偏心率平方
    const double k0 = 0.9996; // UTM比例因子

    // 确定UTM区域
    zone = (int)((lon + 180.0) / 6.0) + 1;
    hemisphere = (lat >= 0) ? 'N' : 'S';

    // 转换为弧度
    double lat_rad = lat * M_PI / 180.0;
    double lon_rad = lon * M_PI / 180.0;
    double lon0_rad = ((zone - 1) * 6 - 180 + 3) * M_PI / 180.0; // 中央经线

    double N = a / sqrt(1 - e2 * sin(lat_rad) * sin(lat_rad));
    double T = tan(lat_rad) * tan(lat_rad);
    double C = e2 * cos(lat_rad) * cos(lat_rad) / (1 - e2);
    double A = cos(lat_rad) * (lon_rad - lon0_rad);

    double M = a * ((1 - e2/4 - 3*e2*e2/64 - 5*e2*e2*e2/256) * lat_rad
                   - (3*e2/8 + 3*e2*e2/32 + 45*e2*e2*e2/1024) * sin(2*lat_rad)
                   + (15*e2*e2/256 + 45*e2*e2*e2/1024) * sin(4*lat_rad)
                   - (35*e2*e2*e2/3072) * sin(6*lat_rad));

    utm_x = k0 * N * (A + (1-T+C)*A*A*A/6 + (5-18*T+T*T+72*C-58*e2)*A*A*A*A*A/120) + 500000.0;
    utm_y = k0 * (M + N*tan(lat_rad)*(A*A/2 + (5-T+9*C+4*C*C)*A*A*A*A/24 + (61-58*T+T*T+600*C-330*e2)*A*A*A*A*A*A/720));

    if (lat < 0) {
        utm_y += 10000000.0; // 南半球偏移
    }
}
void LaserMapping::saveGPSReference() {
    if (gps_reference_file_path_.empty()) {
        RCLCPP_WARN(this->get_logger(), "GPS未初始化或参考文件路径为空，无法保存GPS参考");
        return;
    }

    saveGPSReferenceToFile(gps_reference_file_path_);
    gps_reference_saved_ = true;
    RCLCPP_INFO(this->get_logger(), "GPS参考信息已保存到: %s", gps_reference_file_path_.c_str());
}

void LaserMapping::loadGPSReference() {
    if (gps_reference_file_path_.empty()) {
        RCLCPP_ERROR(this->get_logger(), "GPS参考文件路径为空");
        return;
    }

    if (loadGPSReferenceFromFile(gps_reference_file_path_)) {
        RCLCPP_INFO(this->get_logger(), "GPS参考信息已从文件加载: %s", gps_reference_file_path_.c_str());
    } else {
        RCLCPP_ERROR(this->get_logger(), "无法加载GPS参考文件: %s", gps_reference_file_path_.c_str());
    }
}

void LaserMapping::saveGPSReferenceToFile(const std::string& file_path) {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        RCLCPP_ERROR(this->get_logger(), "无法创建GPS参考文件: %s", file_path.c_str());
        return;
    }

    file << "# GPS-SLAM参考位置信息\n";
    file << "# GPS参考UTM坐标\n";
    file << "ref_utm_x: " << std::fixed << std::setprecision(6) << ref_utm_x_ << "\n";
    file << "ref_utm_y: " << std::fixed << std::setprecision(6) << ref_utm_y_ << "\n";
    file << "ref_altitude: " << std::fixed << std::setprecision(6) << ref_altitude_ << "\n";
    file << "utm_zone: " << utm_zone_ << "\n";
    file << "utm_hemisphere: " << utm_hemisphere_ << "\n";
    file << "# SLAM参考位姿(map坐标系)\n";
    file << "ref_slam_x: " << std::fixed << std::setprecision(6) << ref_slam_x_ << "\n";
    file << "ref_slam_y: " << std::fixed << std::setprecision(6) << ref_slam_y_ << "\n";
    file << "ref_slam_z: " << std::fixed << std::setprecision(6) << ref_slam_z_ << "\n";
    file << "ref_slam_yaw: " << std::fixed << std::setprecision(6) << ref_slam_yaw_ << "\n";
    file << "# GPS参考偏航角\n";
    file << "ref_yaw: " << std::fixed << std::setprecision(6) << ref_yaw_ << "\n";
    file << "# GPS-SLAM坐标系旋转角度(弧度)\n";
    file << "slam_to_gps_rotation_angle: " << std::fixed << std::setprecision(6) << slam_to_gps_rotation_angle_ << "\n";
    file << "# 轨迹匹配自动对齐是否完成\n";
    file << "auto_alignment_completed: " << (auto_alignment_completed_ ? "true" : "false") << "\n";

    file.close();
}

bool LaserMapping::loadGPSReferenceFromFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == '#') {
            continue;
        }

        std::istringstream iss(line);
        std::string key;
        if (std::getline(iss, key, ':')) {
            std::string value_str;
            if (std::getline(iss, value_str)) {
                value_str.erase(0, value_str.find_first_not_of(" \t"));

                try {
                    if (key == "ref_utm_x") {
                        ref_utm_x_ = std::stod(value_str);
                    } else if (key == "ref_utm_y") {
                        ref_utm_y_ = std::stod(value_str);
                    } else if (key == "ref_altitude") {
                        ref_altitude_ = std::stod(value_str);
                    } else if (key == "utm_zone") {
                        utm_zone_ = std::stoi(value_str);
                    } else if (key == "utm_hemisphere") {
                        utm_hemisphere_ = value_str[0];
                    } else if (key == "ref_slam_x") {
                        ref_slam_x_ = std::stod(value_str);
                    } else if (key == "ref_slam_y") {
                        ref_slam_y_ = std::stod(value_str);
                    } else if (key == "ref_slam_z") {
                        ref_slam_z_ = std::stod(value_str);
                    } else if (key == "ref_slam_yaw") {
                        ref_slam_yaw_ = std::stod(value_str);
                    } else if (key == "ref_yaw") {
                        ref_yaw_ = std::stod(value_str);
                    } else if (key =="slam_to_gps_rotation_angle") {
                        slam_to_gps_rotation_angle_ = std::stod(value_str);
                    } else if (key == "auto_alignment_completed") {
                        auto_alignment_completed_ = (value_str == "true");
                    }
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "解析GPS参考文件时出错: %s", e.what());
                }
            }
        }
    }

    file.close();
    gps_initialized_ = true;
    return true;
}

bool LaserMapping::calculateGPSInitialPose() {
    if (!gps_data_valid_ || !gps_initialized_) {
        RCLCPP_ERROR(this->get_logger(), "GPS数据无效或未初始化，无法计算初始位姿");
        return false;
    }
    double utm_x, utm_y;
    int zone;
    char hemisphere;
    initialize_pose[3] = current_gps_x_;  // x
    initialize_pose[4] = current_gps_y_;  // y
    initialize_pose[5] = current_gps_z_;  // z
    double gps_yaw_delta = current_gps_yaw_ - ref_yaw_;
    while (gps_yaw_delta > 180) gps_yaw_delta -= 360;
    while (gps_yaw_delta < -180) gps_yaw_delta += 360;
    double gps_yaw_aligned = ref_slam_yaw_ + gps_yaw_delta;
    initialize_pose[0] = 0.0;  // roll
    initialize_pose[1] = 0.0;  // pitch
    initialize_pose[2] = gps_yaw_aligned * M_PI / 180.0;  // yaw (弧度)
    return true;
}

bool LaserMapping::performGPSAssistedRelocalization() {
    if (!gps_initialized_) {
        loadGPSReference();
        if (!gps_initialized_) {
            RCLCPP_ERROR(this->get_logger(), "无法加载GPS参考信息");
            return false;
        }
    }
    if (!gps_data_valid_) {
        RCLCPP_ERROR(this->get_logger(), "当前GPS数据无效");
        return false;
    }
    if (!calculateGPSInitialPose()) {
        RCLCPP_ERROR(this->get_logger(), "GPS初始位姿计算失败");
        return false;
    }
    double original_yaw = initialize_pose[2];
    double best_fitness_score = std::numeric_limits<double>::max();
    double best_yaw_offset = 0.0;
    bool found_any_valid = false;
    for (int iteration = 0; iteration < yaw_iteration_max_; ++iteration) {
        double yaw_offset = iteration * yaw_iteration_step_ * M_PI / 180.0;
        initialize_pose[2] = original_yaw + yaw_offset;
        initial_pose_received_ = true;
        double current_fitness_score = 0.0;
        bool success = ExecuteICPRefinementWithScore(current_fitness_score);
        if (success) {
            successful_yaw_offset_ = yaw_offset * 180.0 / M_PI;
            RCLCPP_INFO(this->get_logger(), "GPS-assisted relocalization successful");
            return true;
        } else {
            if (current_fitness_score < best_fitness_score) {
                best_fitness_score = current_fitness_score;
                best_yaw_offset = yaw_offset * 180.0 / M_PI;
                found_any_valid = true;
            }
        }
        initial_pose_received_ = false;
        if (initialize_pose[2] > M_PI) {
            initialize_pose[2] -= 2 * M_PI;
        }
    }
    if (found_any_valid) {
        double fine_search_range = 10.0;
        double fine_search_step = 2.0;
        int fine_search_count = static_cast<int>(2 * fine_search_range / fine_search_step) + 1;

        for (int fine_iter = 0; fine_iter < fine_search_count; ++fine_iter) {
            double fine_yaw_offset_deg = best_yaw_offset - fine_search_range + fine_iter * fine_search_step;
            double fine_yaw_offset_rad = fine_yaw_offset_deg * M_PI / 180.0;
            initialize_pose[2] = original_yaw + fine_yaw_offset_rad;

            initial_pose_received_ = true;

            bool fine_success = ExecuteICPRefinement();
            if (fine_success) {
                successful_yaw_offset_ = fine_yaw_offset_deg;
                RCLCPP_INFO(this->get_logger(), "GPS-assisted relocalization successful (fine search)");
                return true;
            }
            initial_pose_received_ = false;
            // Handle angle wrapping
            if (initialize_pose[2] > M_PI) {
                initialize_pose[2] -= 2 * M_PI;
            }
        }
    }

    RCLCPP_WARN(this->get_logger(), "GPS-assisted relocalization failed");
    return false;
}

// ==================== GPS质量检测和约束管理实现 ====================

GPSQualityAssessor::GPSQuality LaserMapping::assessCurrentGPSQuality() {
    if (!gps_data_valid_) {
        return GPSQualityAssessor::GPSQuality(); // 返回无效质量
    }
    double horizontal_accuracy = current_gps_horizontal_accuracy_;
    double vertical_accuracy = current_gps_vertical_accuracy_;
    double signal_strength = 0.0;
    return GPSQualityAssessor::assessQuality(
        current_gps_quality_,
        horizontal_accuracy,
        vertical_accuracy,
        signal_strength
    );
}

bool LaserMapping::shouldAddGPSConstraint() {
    if (!gps_factor_enable_ || !gps_data_valid_ || !gps_initialized_) {
        return false;
    }
    current_gps_quality_assessment_ = assessCurrentGPSQuality();
    bool quality_good = (current_gps_quality_assessment_.is_valid &&
                        current_gps_quality_assessment_.horizontal_accuracy <= gps_factor_min_accuracy_ &&
                        current_gps_quality_assessment_.vertical_accuracy <= 0.50 &&
                        current_gps_quality_assessment_.confidence >= gps_factor_min_confidence_);
    if (current_pose_covariance_.rows() >= 6 && current_pose_covariance_.cols() >= 6) {
        double x_variance = current_pose_covariance_(3, 3);
        double y_variance = current_pose_covariance_(4, 4);

        if (x_variance < pose_cov_threshold_ && y_variance < pose_cov_threshold_) {
            return false;
        }
    }
    if (!quality_good) {
        consecutive_bad_gps_count_++;
        if (consecutive_bad_gps_count_ >= max_consecutive_bad_gps_) {
            if (gps_constraint_enabled_) {
                gps_constraint_enabled_ = false;
            }
        }
        return false;
    }
    consecutive_bad_gps_count_ = 0;
    recent_gps_accuracies_.push_back(current_gps_quality_assessment_.horizontal_accuracy);
    if (recent_gps_accuracies_.size() > max_accuracy_history_) {
        recent_gps_accuracies_.pop_front();
    }
    if (!gps_constraint_enabled_) {
        if (recent_gps_accuracies_.size() >= 20) {
            bool all_good = true;
            for (double acc : recent_gps_accuracies_) {
                if (acc > 0.40) { 
                    all_good = false;
                    break;
                }
            }
            if (all_good) {
                gps_constraint_enabled_ = true;
            }
        }
    }

    return gps_constraint_enabled_;
}

bool LaserMapping::shouldAddGPSConstraintPeriodically(size_t keyframe_idx) {
    if (!shouldAddGPSConstraint()) {
        return false;
    }
    if (keyframe_idx - last_gps_constraint_keyframe_idx_ >= gps_constraint_interval_) {
        last_gps_constraint_keyframe_idx_ = keyframe_idx;
        return true;
    }
    return false;
}

void LaserMapping::updateGPSConstraintStatus() {
    auto current_time = this->get_clock()->now().seconds();
    if (gps_data_valid_) {
        last_gps_constraint_time_ = current_time;
    } else {
        if (current_time - last_gps_constraint_time_ > gps_signal_loss_timeout_) {
            handleGPSSignalLoss();
        }
    }
}

void LaserMapping::addGPSConstraintToGraph(size_t keyframe_idx) {
    if (!shouldAddGPSConstraint()) {
        return;
    }
    if (!std::isfinite(current_gps_x_) || !std::isfinite(current_gps_y_) || !std::isfinite(current_gps_z_)) {
        RCLCPP_WARN(this->get_logger(), "GPS数据包含无效值，跳过GPS约束添加");
        return;
    }
    if (gps_constraint_keyframes_.size() >= max_gps_constraints_) {
        gps_constraint_keyframes_.erase(gps_constraint_keyframes_.begin());
    }
    gps_constraint_keyframes_.push_back(keyframe_idx);
    double gps_horizontal_sigma = std::max(current_gps_quality_assessment_.horizontal_accuracy, 1.0) * 5.0;  // 增大5倍
    gtsam::Point3 gps_point(current_gps_x_, current_gps_y_,current_gps_z_);
    if (!std::isfinite(gps_horizontal_sigma) || gps_horizontal_sigma <= 0) {
        RCLCPP_WARN(this->get_logger(), "GPS精度无效: %.3f，跳过GPS约束", gps_horizontal_sigma);
        return;
    }
    gtsam::Vector3 gps_noise_vector = (gtsam::Vector3() <<
        gps_horizontal_sigma*gps_horizontal_sigma,
        gps_horizontal_sigma*gps_horizontal_sigma,
        10000.0*10000.0).finished();
    auto gps_noise = gtsam::noiseModel::Diagonal::Variances(gps_noise_vector);
    gtsam::GPSFactor gps_factor(keyframe_idx, gps_point, gps_noise);
    try {
        std::lock_guard<std::mutex> lock(graph_mutex_);
        gtsam_graph_.add(gps_factor);
        loop_closure_added_=true;
        double x_variance = 0.0, y_variance = 0.0;
        if (current_pose_covariance_.rows() >= 6 && current_pose_covariance_.cols() >= 6) {
            x_variance = current_pose_covariance_(3, 3);
            y_variance = current_pose_covariance_(4, 4);
        }
    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "添加GPS约束时发生错误: %s", e.what());
    }
}

void LaserMapping::handleGPSSignalLoss() {
    if (gps_constraint_enabled_) {
        gps_constraint_enabled_ = false;
        consecutive_bad_gps_count_ = 0;
        recent_gps_accuracies_.clear();
    }
}

void LaserMapping::collectTrajectoryData(double gps_x, double gps_y, double gps_z,
                                          double slam_x, double slam_y, double slam_z) {
    double current_time = rclcpp::Clock().now().seconds();
    if (!gps_trajectory_.empty()) {
        double gps_dist = std::sqrt(std::pow(gps_x - gps_trajectory_.back().x, 2) +
                                   std::pow(gps_y - gps_trajectory_.back().y, 2));
        double slam_dist = std::sqrt(std::pow(slam_x - slam_trajectory_.back().x, 2) +
                                    std::pow(slam_y - slam_trajectory_.back().y, 2));
        if (gps_dist < min_movement_threshold_ && slam_dist < min_movement_threshold_) {
            return;
        }
    }
    TrajectoryPoint gps_point = {gps_x, gps_y, gps_z, current_time};
    gps_trajectory_.push_back(gps_point);
    if (gps_trajectory_.size() > trajectory_buffer_size_) {
        gps_trajectory_.pop_front();
    }
    TrajectoryPoint slam_point = {slam_x, slam_y, slam_z, current_time};
    slam_trajectory_.push_back(slam_point);
    if (slam_trajectory_.size() > trajectory_buffer_size_) {
        slam_trajectory_.pop_front();
    }

    // 添加调试信息：打印轨迹缓冲区大小
    RCLCPP_DEBUG(this->get_logger(),
                "轨迹缓冲区状态 - GPS轨迹点数: %zu, SLAM轨迹点数: %zu, 缓冲区大小限制: %d",
                gps_trajectory_.size(), slam_trajectory_.size(), trajectory_buffer_size_);

    if (gps_trajectory_.size() >= trajectory_buffer_size_ &&
        slam_trajectory_.size() >= trajectory_buffer_size_ &&
        !auto_alignment_completed_) {
        RCLCPP_INFO(this->get_logger(),
                   "轨迹缓冲区已满，开始执行轨迹对齐 - GPS: %zu点, SLAM: %zu点",
                   gps_trajectory_.size(), slam_trajectory_.size());
        performTrajectoryAlignment();
    }
}

void LaserMapping::performTrajectoryAlignment() {
    Eigen::Vector2d gps_direction = calculateTrajectoryDirection(gps_trajectory_);
    Eigen::Vector2d slam_direction = calculateTrajectoryDirection(slam_trajectory_);
    double angle_diff = std::atan2(gps_direction.y(), gps_direction.x()) -
                       std::atan2(slam_direction.y(), slam_direction.x());
    while (angle_diff > M_PI) angle_diff -= 2 * M_PI;
    while (angle_diff < -M_PI) angle_diff += 2 * M_PI;
    estimated_rotation_angle_ = angle_diff;
    auto_alignment_completed_ = true;
    RCLCPP_INFO(this->get_logger(), "Trajectory alignment completed");
    slam_to_gps_rotation_angle_ = estimated_rotation_angle_;
    saveGPSReference();
}

Eigen::Vector2d LaserMapping::calculateTrajectoryDirection(const std::deque<TrajectoryPoint>& trajectory) {
    if (trajectory.size() < 2) {
        return Eigen::Vector2d(1.0, 0.0); 
    }
    double sum_x = 0, sum_y = 0, sum_xx = 0, sum_xy = 0;
    int n = trajectory.size();
    for (const auto& point : trajectory) {
        sum_x += point.x;
        sum_y += point.y;
        sum_xx += point.x * point.x;
        sum_xy += point.x * point.y;
    }
    double mean_x = sum_x / n;
    double mean_y = sum_y / n;
    double cov_xx = sum_xx / n - mean_x * mean_x;
    double cov_xy = sum_xy / n - mean_x * mean_y;
    double cov_yy = 0;
    for (const auto& point : trajectory) {
        cov_yy += (point.y - mean_y) * (point.y - mean_y);
    }
    cov_yy /= n;
    double trace = cov_xx + cov_yy;
    double det = cov_xx * cov_yy - cov_xy * cov_xy;
    double lambda1 = (trace + std::sqrt(trace * trace - 4 * det)) / 2;
    Eigen::Vector2d direction;
    if (std::abs(cov_xy) > 1e-6) {
        direction = Eigen::Vector2d(lambda1 - cov_yy, cov_xy);
    } else {
        direction = Eigen::Vector2d(1.0, 0.0);
    }
    direction.normalize();
    return direction;
}
}
