#include <nav_msgs/Odometry.h>
#include <ros/ros.h>
#include <tf/transform_datatypes.h>
#include <iomanip>
#include <iostream>

class OdometrySubscriber {
   public:
    OdometrySubscriber() {
        // 初始化ROS节点句柄
        ros::NodeHandle nh;

        // 订阅/Odometry话题
        odom_sub_ = nh.subscribe("/Odometry", 10, &OdometrySubscriber::odometryCallback, this);

        ROS_INFO("Odometry Subscriber initialized. Waiting for /Odometry messages...");
    }

   private:
    ros::Subscriber odom_sub_;

    void odometryCallback(const nav_msgs::Odometry::ConstPtr& msg) {
        // 获取位置信息
        double x = msg->pose.pose.position.x;
        double y = msg->pose.pose.position.y;
        double z = msg->pose.pose.position.z;

        // 获取四元数
        geometry_msgs::Quaternion quat = msg->pose.pose.orientation;

        // 将四元数转换为欧拉角
        tf::Quaternion tf_quat(quat.x, quat.y, quat.z, quat.w);
        tf::Matrix3x3 mat(tf_quat);
        double roll, pitch, yaw;
        mat.getRPY(roll, pitch, yaw);

        // 将弧度转换为度
        double yaw_deg = yaw * 180.0 / M_PI;
        double roll_deg = roll * 180.0 / M_PI;
        double pitch_deg = pitch * 180.0 / M_PI;

        // // 打印信息
        // std::cout << std::fixed << std::setprecision(3);
        // std::cout << "=== Odometry Info ===" << std::endl;
        // std::cout << "Position: x=" << x << ", y=" << y << ", z=" << z << std::endl;
        // std::cout << "Orientation: roll=" << roll_deg << "°, pitch=" << pitch_deg << "°, yaw=" << yaw_deg << "°" <<
        // std::endl; std::cout << "Yaw (rad): " << yaw << std::endl; std::cout << "Timestamp: " << msg->header.stamp <<
        // std::endl; std::cout << "Frame ID: " << msg->header.frame_id << std::endl; std::cout << "Child Frame ID: " <<
        // msg->child_frame_id << std::endl; std::cout << "--------------------" << std::endl;

        // 也可以使用ROS_INFO输出
        ROS_INFO("Odom - x: %.3f, y: %.3f, yaw: %.3f° (%.3f rad)", x, y, yaw_deg, yaw);
    }
};

int main(int argc, char** argv) {
    // 初始化ROS节点
    ros::init(argc, argv, "odometry_subscriber");

    // 创建订阅器对象
    OdometrySubscriber subscriber;

    ROS_INFO("Odometry subscriber node started. Press Ctrl+C to exit.");

    // 进入ROS事件循环
    ros::spin();

    return 0;
}
